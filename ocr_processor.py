"""
OCR文字识别处理模块
"""
import cv2
import numpy as np
from PIL import Image
from typing import List, Tuple, Optional
import logging
import re

# PaddleOCR
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    logging.warning("PaddleOCR未安装，将使用备选方案")

# Tesseract OCR (备选方案)
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

logger = logging.getLogger(__name__)

class OCRProcessor:
    def __init__(self, engine="paddleocr", lang="ch"):
        self.engine = engine
        self.lang = lang
        self.ocr_instance = None
        
        self._init_ocr_engine()
    
    def _init_ocr_engine(self):
        """初始化OCR引擎"""
        if self.engine == "paddleocr" and PADDLEOCR_AVAILABLE:
            try:
                self.ocr_instance = PaddleOCR(
                    use_angle_cls=True, 
                    lang=self.lang,
                    show_log=False
                )
                logger.info("PaddleOCR初始化成功")
            except Exception as e:
                logger.error(f"PaddleOCR初始化失败: {e}")
                self._fallback_to_tesseract()
        
        elif self.engine == "tesseract" and TESSERACT_AVAILABLE:
            self._init_tesseract()
        
        else:
            logger.warning("未找到可用的OCR引擎")
    
    def _fallback_to_tesseract(self):
        """回退到Tesseract"""
        if TESSERACT_AVAILABLE:
            self.engine = "tesseract"
            self._init_tesseract()
        else:
            logger.error("无可用的OCR引擎")
    
    def _init_tesseract(self):
        """初始化Tesseract"""
        try:
            # 设置Tesseract配置
            self.tesseract_config = '--oem 3 --psm 6 -l chi_sim'
            logger.info("Tesseract初始化成功")
        except Exception as e:
            logger.error(f"Tesseract初始化失败: {e}")
    
    def preprocess_image(self, image: Image.Image) -> np.ndarray:
        """图像预处理，提高OCR识别率"""
        try:
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 转换为灰度图
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # 高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 自适应阈值二值化
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作，去除噪点
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return np.array(image)
    
    def extract_text_paddleocr(self, image: Image.Image) -> List[str]:
        """使用PaddleOCR提取文字"""
        if not self.ocr_instance:
            return []
        
        try:
            # 预处理图像
            processed_image = self.preprocess_image(image)
            
            # OCR识别
            results = self.ocr_instance.ocr(processed_image, cls=True)
            
            texts = []
            if results and results[0]:
                for line in results[0]:
                    if line and len(line) >= 2:
                        text = line[1][0]  # 提取文字内容
                        confidence = line[1][1]  # 置信度
                        
                        # 过滤低置信度结果
                        if confidence > 0.5:
                            texts.append(text.strip())
            
            return texts
            
        except Exception as e:
            logger.error(f"PaddleOCR识别失败: {e}")
            return []
    
    def extract_text_tesseract(self, image: Image.Image) -> List[str]:
        """使用Tesseract提取文字"""
        try:
            # 预处理图像
            processed_image = self.preprocess_image(image)
            processed_pil = Image.fromarray(processed_image)
            
            # OCR识别
            text = pytesseract.image_to_string(
                processed_pil, 
                config=self.tesseract_config
            )
            
            # 分割成行并清理
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            return lines
            
        except Exception as e:
            logger.error(f"Tesseract识别失败: {e}")
            return []
    
    def extract_text(self, image: Image.Image) -> List[str]:
        """提取图像中的文字"""
        if self.engine == "paddleocr":
            return self.extract_text_paddleocr(image)
        elif self.engine == "tesseract":
            return self.extract_text_tesseract(image)
        else:
            logger.error("无可用的OCR引擎")
            return []
    
    def filter_messages(self, texts: List[str]) -> List[str]:
        """过滤和清理识别出的文字，提取有效消息"""
        if not texts:
            return []
        
        filtered_messages = []
        
        for text in texts:
            # 清理文字
            cleaned_text = self._clean_text(text)
            
            # 过滤无效内容
            if self._is_valid_message(cleaned_text):
                filtered_messages.append(cleaned_text)
        
        return filtered_messages
    
    def _clean_text(self, text: str) -> str:
        """清理文字内容"""
        if not text:
            return ""
        
        # 移除多余空白字符
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留中文、英文、数字、常用标点）
        cleaned = re.sub(r'[^\u4e00-\u9fff\w\s.,!?;:()（）。，！？；：""''【】]', '', cleaned)
        
        return cleaned
    
    def _is_valid_message(self, text: str) -> bool:
        """判断是否为有效消息"""
        if not text or len(text) < 2:
            return False
        
        # 过滤系统消息和无意义内容
        invalid_patterns = [
            r'^\d{1,2}:\d{2}$',  # 时间格式
            r'^[0-9]+$',         # 纯数字
            r'^[a-zA-Z]$',       # 单个字母
            r'系统消息',
            r'撤回了一条消息',
            r'\[图片\]',
            r'\[视频\]',
            r'\[文件\]',
            r'\[语音\]',
            r'微信',
            r'聊天记录'
        ]
        
        for pattern in invalid_patterns:
            if re.search(pattern, text):
                return False
        
        return True
    
    def get_latest_message(self, texts: List[str]) -> Optional[str]:
        """获取最新的消息（通常是列表中的最后一条有效消息）"""
        filtered_messages = self.filter_messages(texts)
        
        if filtered_messages:
            # 返回最后一条消息
            return filtered_messages[-1]
        
        return None
