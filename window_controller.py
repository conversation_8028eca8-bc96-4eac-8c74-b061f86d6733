"""
微信窗口控制和自动化操作模块
"""
import time
import random
import pyautogui
import pygetwindow as gw
from PIL import Image
import cv2
import numpy as np
from typing import Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)

class WindowController:
    def __init__(self):
        # 设置pyautogui安全模式
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
        self.wechat_window = None
        self.chat_area_region = None
        self.input_area_region = None
        
    def find_wechat_window(self) -> Optional[gw.Win32Window]:
        """查找微信窗口"""
        try:
            windows = gw.getWindowsWithTitle("微信")
            if not windows:
                logger.warning("未找到微信窗口")
                return None
            
            # 选择第一个微信窗口
            wechat_window = windows[0]
            if wechat_window.isMinimized:
                logger.info("微信窗口已最小化，正在恢复...")
                wechat_window.restore()
            
            self.wechat_window = wechat_window
            logger.info(f"找到微信窗口: {wechat_window.title}")
            return wechat_window
            
        except Exception as e:
            logger.error(f"查找微信窗口失败: {e}")
            return None
    
    def get_window_region(self) -> Optional[Tuple[int, int, int, int]]:
        """获取微信窗口区域"""
        if not self.wechat_window:
            self.find_wechat_window()
        
        if not self.wechat_window:
            return None
        
        try:
            # 获取窗口位置和大小
            left = self.wechat_window.left
            top = self.wechat_window.top
            width = self.wechat_window.width
            height = self.wechat_window.height
            
            return (left, top, width, height)
        except Exception as e:
            logger.error(f"获取窗口区域失败: {e}")
            return None
    
    def capture_window(self) -> Optional[Image.Image]:
        """截取微信窗口"""
        region = self.get_window_region()
        if not region:
            return None
        
        try:
            left, top, width, height = region
            screenshot = pyautogui.screenshot(region=(left, top, width, height))
            return screenshot
        except Exception as e:
            logger.error(f"截取窗口失败: {e}")
            return None
    
    def capture_chat_area(self) -> Optional[Image.Image]:
        """截取聊天区域"""
        window_screenshot = self.capture_window()
        if not window_screenshot:
            return None
        
        try:
            # 估算聊天区域（通常在窗口的右侧中间部分）
            width, height = window_screenshot.size
            
            # 聊天区域大致位置（需要根据实际微信界面调整）
            chat_left = int(width * 0.25)  # 左侧联系人列表占25%
            chat_top = int(height * 0.1)   # 顶部标题栏占10%
            chat_width = int(width * 0.75) # 聊天区域占75%
            chat_height = int(height * 0.7) # 聊天区域占70%（底部是输入框）
            
            chat_area = window_screenshot.crop((
                chat_left, chat_top, 
                chat_left + chat_width, 
                chat_top + chat_height
            ))
            
            self.chat_area_region = (chat_left, chat_top, chat_width, chat_height)
            return chat_area
            
        except Exception as e:
            logger.error(f"截取聊天区域失败: {e}")
            return None
    
    def send_message(self, message: str, delay_range: Tuple[float, float] = (1, 3)):
        """发送消息到微信"""
        if not self.wechat_window:
            logger.error("微信窗口未找到")
            return False
        
        try:
            # 确保微信窗口处于前台
            self.wechat_window.activate()
            time.sleep(0.5)
            
            # 点击输入框区域（估算位置）
            window_region = self.get_window_region()
            if not window_region:
                return False
            
            left, top, width, height = window_region
            input_x = left + int(width * 0.6)  # 输入框大致在窗口60%位置
            input_y = top + int(height * 0.85)  # 输入框在窗口85%高度位置
            
            pyautogui.click(input_x, input_y)
            time.sleep(0.2)
            
            # 清空输入框
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            
            # 输入消息
            pyautogui.write(message, interval=0.05)
            
            # 随机延迟后发送
            delay = random.uniform(*delay_range)
            time.sleep(delay)
            
            # 发送消息
            pyautogui.press('enter')
            
            logger.info(f"已发送消息: {message[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    def is_window_active(self) -> bool:
        """检查微信窗口是否处于活动状态"""
        if not self.wechat_window:
            return False
        
        try:
            return self.wechat_window.isActive
        except:
            return False
    
    def detect_new_message_area(self, current_image: Image.Image, 
                               previous_image: Optional[Image.Image]) -> Optional[Tuple[int, int, int, int]]:
        """检测新消息区域（通过图像对比）"""
        if not previous_image:
            return None
        
        try:
            # 转换为OpenCV格式
            current_cv = cv2.cvtColor(np.array(current_image), cv2.COLOR_RGB2BGR)
            previous_cv = cv2.cvtColor(np.array(previous_image), cv2.COLOR_RGB2BGR)
            
            # 计算差异
            diff = cv2.absdiff(current_cv, previous_cv)
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
            
            # 阈值处理
            _, thresh = cv2.threshold(gray_diff, 30, 255, cv2.THRESH_BINARY)
            
            # 查找轮廓
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 找到最大的变化区域
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)
                
                # 过滤太小的变化
                if w * h > 1000:  # 最小面积阈值
                    return (x, y, w, h)
            
            return None
            
        except Exception as e:
            logger.error(f"检测新消息区域失败: {e}")
            return None
