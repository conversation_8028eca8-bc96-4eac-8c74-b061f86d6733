# 🪟 微信窗口标题管理器使用指南

## 🎯 解决的问题

**原问题：** 微信对话窗口标题不是"微信"，而是"微信 - 联系人名字"或"微信 - 群名"，导致程序无法找到正确的窗口。

**解决方案：** 使用窗口标题管理器，可以自定义监控最多10个不同的微信窗口标题，实现精确的窗口监控。

## 🚀 快速开始

### 第一步：启动窗口标题管理器

**方法一：使用启动菜单**
```bash
# 双击运行
启动程序.bat
# 选择 "4. 🪟 窗口标题管理器"
```

**方法二：直接启动**
```bash
# 双击运行
python 窗口标题管理器.py
# 或双击
窗口管理器.bat
```

### 第二步：检测当前微信窗口

1. **打开微信PC版**
   - 确保微信已登录
   - 打开要监控的聊天对话

2. **刷新窗口列表**
   - 在管理器中点击"🔄 刷新窗口列表"
   - 查看左侧"当前微信窗口检测"区域

3. **查看检测结果**
   - 应该能看到类似这样的窗口：
     - "微信 - 张三"
     - "微信 - 工作群"
     - "微信 - 文件传输助手"

### 第三步：添加监控窗口

**方法一：从检测列表添加**
1. 在左侧窗口列表中选择要监控的窗口
2. 点击"➕ 添加到监控列表"
3. 窗口标题会自动添加到右侧监控配置中

**方法二：手动添加**
1. 点击右侧的"➕ 手动添加"按钮
2. 输入完整的窗口标题，如："微信 - 张三"
3. 点击"添加"

### 第四步：管理监控配置

在右侧"监控窗口配置"区域可以：

- **✏️ 编辑**：修改窗口标题或启用状态
- **🔄 切换启用**：快速启用/禁用某个监控项
- **❌ 删除**：删除不需要的监控项

### 第五步：保存并测试

1. **保存配置**
   - 点击"💾 保存配置"
   - 配置会保存到 `window_titles.json` 文件

2. **测试监控**
   - 点击"🧪 测试监控"
   - 查看测试结果，确认能找到配置的窗口

3. **启动主程序**
   - 点击"🚀 启动主程序"
   - 主程序会自动使用新的窗口配置

## 🔧 界面功能详解

### 左侧：当前微信窗口检测

**功能：**
- 实时检测系统中所有包含"微信"的窗口
- 显示窗口标题、大小、状态
- 提供快速添加到监控列表的功能

**操作：**
- 🔄 刷新窗口列表：重新检测当前窗口
- ➕ 添加到监控列表：将选中窗口添加到监控配置

### 右侧：监控窗口配置

**功能：**
- 管理最多10个监控窗口标题
- 显示每个标题的启用状态和最后检测时间
- 提供编辑、删除、启用/禁用功能

**操作：**
- ✏️ 编辑：修改窗口标题和启用状态
- 🔄 切换启用：快速启用/禁用监控项
- ❌ 删除：删除监控项
- ➕ 手动添加：手动输入窗口标题

### 底部：操作和状态

**功能：**
- 保存配置到文件
- 测试监控功能
- 启动主程序
- 显示操作状态

## 📋 使用场景示例

### 场景1：监控特定联系人

1. **目标**：只监控与"张三"的对话
2. **操作**：
   - 打开与张三的聊天窗口
   - 在管理器中添加"微信 - 张三"
   - 禁用其他监控项
3. **结果**：程序只会监控这个特定对话

### 场景2：监控多个群聊

1. **目标**：监控"工作群"和"项目群"
2. **操作**：
   - 分别打开两个群聊
   - 添加"微信 - 工作群"
   - 添加"微信 - 项目群"
3. **结果**：程序会同时监控两个群聊

### 场景3：监控文件传输助手

1. **目标**：在文件传输助手中测试功能
2. **操作**：
   - 打开文件传输助手
   - 添加"微信 - 文件传输助手"
3. **结果**：可以安全地测试自动回复功能

## ⚙️ 配置文件说明

### window_titles.json
```json
{
  "monitored_titles": [
    {
      "title": "微信 - 张三",
      "enabled": true,
      "added_time": "2024-01-01T12:00:00",
      "last_seen": "2024-01-01T12:30:00"
    }
  ],
  "last_updated": "2024-01-01T12:30:00"
}
```

### window_config.json（自动生成）
```json
{
  "WECHAT_WINDOW_TITLES": [
    "微信 - 张三",
    "微信 - 工作群"
  ],
  "PRIMARY_TITLE": "微信 - 张三"
}
```

## 🔍 故障排除

### 问题1：找不到微信窗口
**解决方案：**
- 确保微信PC版已打开
- 确保要监控的聊天窗口已打开
- 点击"🔄 刷新窗口列表"重新检测

### 问题2：窗口标题不正确
**解决方案：**
- 检查微信窗口的实际标题
- 使用"✏️ 编辑"功能修正标题
- 确保标题完全匹配（包括空格和标点）

### 问题3：监控不生效
**解决方案：**
- 确认监控项已启用（显示✅）
- 运行"🧪 测试监控"检查状态
- 重新启动主程序应用新配置

### 问题4：配置丢失
**解决方案：**
- 检查 `window_titles.json` 文件是否存在
- 重新配置并保存
- 确保有文件写入权限

## 💡 使用技巧

### 1. 窗口标题命名规律
- 个人聊天：`微信 - 联系人备注名`
- 群聊：`微信 - 群名称`
- 文件传输助手：`微信 - 文件传输助手`

### 2. 优先级设置
- 启用的第一个标题会作为主要监控目标
- 可以通过编辑调整顺序

### 3. 测试建议
- 首次使用建议先在文件传输助手中测试
- 确认功能正常后再监控重要对话

### 4. 性能优化
- 不要同时监控太多窗口（建议3-5个）
- 定期清理不需要的监控项

## 🎉 完成配置后

配置完成后，您可以：

1. **启动主程序**：点击"🚀 启动主程序"
2. **开始监控**：在主程序中点击"开始监控"
3. **测试功能**：发送测试消息验证自动回复
4. **享受智能对话**：体验AI驱动的自动回复

---

**现在您可以精确控制要监控哪些微信对话了！** 🎊
