"""
真正的微信自动回复器 - 实际监控对话内容并自动回复
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pygetwindow as gw
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageGrab
import time
import threading
import requests
import json
import os
import re
from datetime import datetime
from dotenv import load_dotenv

# 禁用pyautogui的安全模式，允许鼠标移动到屏幕边缘
pyautogui.FAILSAFE = False
pyautogui.PAUSE = 0.1

# 加载环境变量
load_dotenv()

class RealWeChatReplier:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("真正的微信自动回复器")
        self.root.geometry("1200x800")
        
        # 核心变量
        self.target_window = None
        self.target_title = ""
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_screenshot = None
        self.last_message_hash = ""
        
        # 区域坐标
        self.chat_area = None
        self.input_area = None
        self.send_button_area = None
        
        # API配置
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎯 真正的微信自动回复器", font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 步骤1：选择窗口
        step1_frame = ttk.LabelFrame(main_frame, text="步骤1：选择微信对话窗口", padding="10")
        step1_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step1_frame.columnconfigure(1, weight=1)
        
        ttk.Button(step1_frame, text="🔄 刷新", command=self.refresh_windows).grid(row=0, column=0, padx=(0, 10))
        
        self.window_var = tk.StringVar()
        self.window_combo = ttk.Combobox(step1_frame, textvariable=self.window_var, width=60, state="readonly")
        self.window_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(step1_frame, text="✅ 选择", command=self.select_window).grid(row=0, column=2)
        
        self.selected_label = ttk.Label(step1_frame, text="未选择窗口", foreground="red")
        self.selected_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 步骤2：定位区域
        step2_frame = ttk.LabelFrame(main_frame, text="步骤2：定位微信区域", padding="10")
        step2_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(step2_frame, text="📸 截图分析", command=self.analyze_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step2_frame, text="🎯 手动定位", command=self.manual_locate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step2_frame, text="🧪 测试发送", command=self.test_send).pack(side=tk.LEFT, padx=(0, 10))
        
        self.locate_label = ttk.Label(step2_frame, text="未定位区域", foreground="red")
        self.locate_label.pack(side=tk.RIGHT)
        
        # 步骤3：监控和日志
        step3_frame = ttk.LabelFrame(main_frame, text="步骤3：监控和回复", padding="10")
        step3_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        step3_frame.columnconfigure(0, weight=1)
        step3_frame.rowconfigure(1, weight=1)
        
        # 控制按钮
        control_frame = ttk.Frame(step3_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="🚀 开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ 停止", command=self.stop_monitoring, state="disabled")
        self.stop_btn.pack(side=tk.LEFT)
        
        # 监控日志
        self.log_text = scrolledtext.ScrolledText(step3_frame, height=15, font=("Consolas", 9))
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：测试区域
        test_frame = ttk.LabelFrame(main_frame, text="测试区域", padding="10")
        test_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        test_frame.columnconfigure(0, weight=1)
        test_frame.rowconfigure(2, weight=1)
        
        # 测试消息
        ttk.Label(test_frame, text="测试消息:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.test_msg_var = tk.StringVar(value="你好")
        ttk.Entry(test_frame, textvariable=self.test_msg_var).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # AI回复区域
        ttk.Label(test_frame, text="AI回复:").grid(row=2, column=0, sticky=(tk.W, tk.N), pady=(0, 5))
        self.reply_text = scrolledtext.ScrolledText(test_frame, height=10, font=("微软雅黑", 9))
        self.reply_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 测试按钮
        test_btn_frame = ttk.Frame(test_frame)
        test_btn_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(test_btn_frame, text="🤖 获取AI回复", command=self.get_ai_reply).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_btn_frame, text="📤 发送回复", command=self.send_reply).pack(side=tk.LEFT)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        self.log("🎯 真正的微信自动回复器已启动")
        self.log("请按步骤操作：1.选择窗口 → 2.定位区域 → 3.开始监控")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
    
    def refresh_windows(self):
        """刷新微信窗口列表"""
        try:
            self.log("🔍 正在刷新微信窗口列表...")
            
            all_windows = gw.getAllWindows()
            wechat_windows = []
            
            for window in all_windows:
                title = window.title.strip()
                if ("微信" in title and 
                    len(title) > 0 and 
                    window.visible and 
                    window.width > 400 and 
                    window.height > 300):
                    wechat_windows.append(f"{title} ({window.width}x{window.height})")
            
            self.window_combo['values'] = wechat_windows
            
            if wechat_windows:
                self.log(f"✅ 找到 {len(wechat_windows)} 个微信窗口")
                if len(wechat_windows) == 1:
                    self.window_combo.current(0)
            else:
                self.log("❌ 未找到微信窗口")
                
        except Exception as e:
            self.log(f"❌ 刷新失败: {e}")
    
    def select_window(self):
        """选择目标窗口"""
        selected = self.window_var.get()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        try:
            self.target_title = selected.split(" (")[0]
            windows = gw.getWindowsWithTitle(self.target_title)
            
            if windows:
                self.target_window = windows[0]
                self.target_window.activate()
                time.sleep(0.5)
                
                self.selected_label.config(text=f"✅ 已选择: {self.target_title}", foreground="green")
                self.log(f"✅ 目标窗口: {self.target_title}")
                
                # 自动分析窗口
                self.analyze_window()
                return True
            else:
                self.log(f"❌ 找不到窗口: {self.target_title}")
                return False
                
        except Exception as e:
            self.log(f"❌ 选择窗口失败: {e}")
            return False
    
    def analyze_window(self):
        """分析窗口结构"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择窗口")
            return
        
        try:
            self.log("📸 正在分析窗口结构...")
            
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.5)
            
            # 获取窗口坐标
            left = self.target_window.left
            top = self.target_window.top
            width = self.target_window.width
            height = self.target_window.height
            
            self.log(f"窗口位置: ({left}, {top}), 大小: {width}x{height}")
            
            # 估算各区域位置
            # 聊天区域（右侧上部分）
            chat_left = left + int(width * 0.25)
            chat_top = top + int(height * 0.1)
            chat_width = int(width * 0.75)
            chat_height = int(height * 0.65)
            self.chat_area = (chat_left, chat_top, chat_width, chat_height)
            
            # 输入区域（右侧下部分）
            input_left = left + int(width * 0.25)
            input_top = top + int(height * 0.8)
            input_width = int(width * 0.65)
            input_height = int(height * 0.15)
            self.input_area = (input_left, input_top, input_width, input_height)
            
            # 发送按钮区域
            send_left = left + int(width * 0.9)
            send_top = top + int(height * 0.85)
            send_width = int(width * 0.08)
            send_height = int(height * 0.08)
            self.send_button_area = (send_left, send_top, send_width, send_height)
            
            self.log(f"聊天区域: {self.chat_area}")
            self.log(f"输入区域: {self.input_area}")
            self.log(f"发送按钮: {self.send_button_area}")
            
            # 截图保存
            screenshot = ImageGrab.grab(bbox=(left, top, left + width, top + height))
            screenshot.save("window_analysis.png")
            self.log("✅ 窗口分析完成，截图已保存")
            
            self.locate_label.config(text="✅ 区域已定位", foreground="green")
            
        except Exception as e:
            self.log(f"❌ 分析窗口失败: {e}")
    
    def manual_locate(self):
        """手动定位区域"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择窗口")
            return
        
        self.log("🎯 开始手动定位...")
        self.log("请按以下步骤操作:")
        self.log("1. 将鼠标移动到输入框中央")
        self.log("2. 等待3秒后程序会记录位置")
        
        def record_position():
            time.sleep(3)
            x, y = pyautogui.position()
            
            # 基于鼠标位置估算区域
            window_left = self.target_window.left
            window_top = self.target_window.top
            window_width = self.target_window.width
            window_height = self.target_window.height
            
            # 输入区域以鼠标位置为中心
            input_left = x - 100
            input_top = y - 20
            input_width = 200
            input_height = 40
            self.input_area = (input_left, input_top, input_width, input_height)
            
            # 发送按钮在输入框右侧
            send_left = x + 120
            send_top = y - 15
            send_width = 60
            send_height = 30
            self.send_button_area = (send_left, send_top, send_width, send_height)
            
            # 聊天区域在输入框上方
            chat_left = window_left + int(window_width * 0.25)
            chat_top = window_top + int(window_height * 0.1)
            chat_width = int(window_width * 0.75)
            chat_height = y - chat_top - 50
            self.chat_area = (chat_left, chat_top, chat_width, chat_height)
            
            self.log(f"✅ 手动定位完成")
            self.log(f"输入框位置: ({input_left}, {input_top})")
            self.log(f"发送按钮位置: ({send_left}, {send_top})")
            
            self.locate_label.config(text="✅ 手动定位完成", foreground="green")
        
        threading.Thread(target=record_position, daemon=True).start()
    
    def test_send(self):
        """测试发送功能"""
        if not self.input_area:
            messagebox.showwarning("警告", "请先定位区域")
            return
        
        test_message = "这是一条测试消息"
        self.log(f"🧪 测试发送: {test_message}")
        
        if self._send_message_to_wechat(test_message):
            self.log("✅ 测试发送成功")
        else:
            self.log("❌ 测试发送失败")
    
    def _send_message_to_wechat(self, message):
        """发送消息到微信"""
        try:
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.3)
            
            # 点击输入框
            input_x = self.input_area[0] + self.input_area[2] // 2
            input_y = self.input_area[1] + self.input_area[3] // 2
            
            self.log(f"点击输入框: ({input_x}, {input_y})")
            pyautogui.click(input_x, input_y)
            time.sleep(0.2)
            
            # 清空输入框
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            pyautogui.press('delete')
            time.sleep(0.1)
            
            # 输入消息
            pyautogui.write(message, interval=0.01)
            time.sleep(0.3)
            
            # 发送消息 - 尝试多种方式
            # 方式1: 按回车键
            pyautogui.press('enter')
            time.sleep(0.2)
            
            # 方式2: 如果有发送按钮，点击发送按钮
            if self.send_button_area:
                send_x = self.send_button_area[0] + self.send_button_area[2] // 2
                send_y = self.send_button_area[1] + self.send_button_area[3] // 2
                pyautogui.click(send_x, send_y)
                time.sleep(0.2)
            
            return True
            
        except Exception as e:
            self.log(f"❌ 发送消息失败: {e}")
            return False
    
    def get_ai_reply(self):
        """获取AI回复"""
        message = self.test_msg_var.get().strip()
        if not message:
            messagebox.showwarning("警告", "请输入测试消息")
            return
        
        self.log(f"🤖 获取AI回复: {message}")
        self.reply_text.delete(1.0, tk.END)
        self.reply_text.insert(tk.END, "正在获取AI回复...")
        
        def get_reply():
            try:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": "anthropic/claude-3.5-sonnet",
                    "messages": [
                        {"role": "system", "content": "你是一个友善的AI助手，请用中文简洁回复，不超过50字。"},
                        {"role": "user", "content": message}
                    ],
                    "max_tokens": 100,
                    "temperature": 0.7
                }
                
                response = requests.post(f"{self.base_url}/chat/completions", 
                                       headers=headers, json=data, timeout=15)
                
                if response.status_code == 200:
                    result = response.json()
                    reply = result["choices"][0]["message"]["content"].strip()
                    
                    self.reply_text.delete(1.0, tk.END)
                    self.reply_text.insert(tk.END, reply)
                    self.log(f"✅ AI回复: {reply}")
                else:
                    self.reply_text.delete(1.0, tk.END)
                    self.reply_text.insert(tk.END, f"API调用失败: {response.status_code}")
                    self.log(f"❌ API调用失败: {response.status_code}")
                    
            except Exception as e:
                self.reply_text.delete(1.0, tk.END)
                self.reply_text.insert(tk.END, f"获取回复失败: {e}")
                self.log(f"❌ 获取AI回复失败: {e}")
        
        threading.Thread(target=get_reply, daemon=True).start()
    
    def send_reply(self):
        """发送回复"""
        reply = self.reply_text.get(1.0, tk.END).strip()
        if not reply:
            messagebox.showwarning("警告", "没有回复内容")
            return
        
        if not self.input_area:
            messagebox.showwarning("警告", "请先定位区域")
            return
        
        self.log(f"📤 发送回复: {reply}")
        
        if self._send_message_to_wechat(reply):
            self.log("✅ 回复发送成功")
        else:
            self.log("❌ 回复发送失败")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.target_window or not self.chat_area:
            messagebox.showwarning("警告", "请先选择窗口并定位区域")
            return
        
        self.is_monitoring = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        self.log("🚀 开始监控微信对话...")
        self.status_var.set("监控中...")
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        self.log("⏹️ 监控已停止")
        self.status_var.set("已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 截取聊天区域
                chat_screenshot = self._capture_chat_area()
                if chat_screenshot is None:
                    time.sleep(2)
                    continue
                
                # 检测新消息
                if self._detect_new_message(chat_screenshot):
                    self.log("📨 检测到新消息")
                    
                    # 简单模拟：假设收到了消息
                    # 实际应该用OCR识别消息内容
                    received_message = "收到新消息"
                    
                    # 获取AI回复
                    ai_reply = self._get_ai_reply_sync(received_message)
                    if ai_reply:
                        # 发送回复
                        if self._send_message_to_wechat(ai_reply):
                            self.log(f"✅ 自动回复: {ai_reply}")
                        else:
                            self.log("❌ 自动回复发送失败")
                    
                    # 更新截图
                    self.last_screenshot = chat_screenshot
                
                time.sleep(3)  # 每3秒检查一次
                
            except Exception as e:
                self.log(f"❌ 监控出错: {e}")
                time.sleep(5)
    
    def _capture_chat_area(self):
        """截取聊天区域"""
        try:
            if not self.chat_area:
                return None
            
            left, top, width, height = self.chat_area
            screenshot = ImageGrab.grab(bbox=(left, top, left + width, top + height))
            return screenshot
            
        except Exception as e:
            self.log(f"❌ 截取聊天区域失败: {e}")
            return None
    
    def _detect_new_message(self, current_screenshot):
        """检测新消息"""
        if self.last_screenshot is None:
            self.last_screenshot = current_screenshot
            return False
        
        try:
            # 转换为numpy数组
            current_array = np.array(current_screenshot)
            last_array = np.array(self.last_screenshot)
            
            # 计算差异
            diff = cv2.absdiff(current_array, last_array)
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_RGB2GRAY)
            
            # 计算变化程度
            change_pixels = np.sum(gray_diff > 30)
            total_pixels = gray_diff.shape[0] * gray_diff.shape[1]
            change_ratio = change_pixels / total_pixels
            
            # 如果变化超过阈值，认为有新消息
            if change_ratio > 0.02:  # 2%的像素发生变化
                self.log(f"检测到变化: {change_ratio:.3f}")
                return True
            
            return False
            
        except Exception as e:
            self.log(f"❌ 消息检测失败: {e}")
            return False
    
    def _get_ai_reply_sync(self, message):
        """同步获取AI回复"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "anthropic/claude-3.5-sonnet",
                "messages": [
                    {"role": "system", "content": "你是一个友善的AI助手，请用中文简洁回复，不超过30字。"},
                    {"role": "user", "content": message}
                ],
                "max_tokens": 80,
                "temperature": 0.7
            }
            
            response = requests.post(f"{self.base_url}/chat/completions", 
                                   headers=headers, json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"].strip()
            else:
                self.log(f"❌ API调用失败: {response.status_code}")
                return "抱歉，我现在无法回复。"
                
        except Exception as e:
            self.log(f"❌ 获取AI回复失败: {e}")
            return "收到您的消息了。"
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    app = RealWeChatReplier()
    app.run()

if __name__ == "__main__":
    main()
