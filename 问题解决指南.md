# 🛠️ 微信自动回复智能体 - 问题解决指南

## ✅ 当前状态

**好消息！您的程序配置已完成，所有测试都通过了：**

- ✅ 所有依赖包已安装
- ✅ OpenRouter API配置正确
- ✅ 微信窗口检测正常
- ✅ API连接测试通过（323个模型可用）

## 🚀 推荐启动方式

### 方法一：使用新的启动菜单（推荐）
```bash
# 双击运行
启动程序.bat
```
这会显示一个菜单，让您选择：
1. 🚀 启动主程序 (完整功能)
2. 🧪 功能测试工具 (推荐首次使用)
3. 🔧 配置管理器
4. 📋 查看使用说明

### 方法二：直接启动测试工具
```bash
# 双击运行或命令行执行
python simple_test.py
```

### 方法三：传统启动方式
```bash
# 如果上述方式都不行，使用这个
python main.py
```

## 🔧 如果启动仍有问题

### 1. 检查Python环境
```bash
python --version
# 应该显示 Python 3.8 或更高版本
```

### 2. 手动安装依赖
```bash
pip install pyautogui pygetwindow opencv-python pillow openai python-dotenv pyyaml requests
```

### 3. 检查配置文件
确保 `.env` 文件存在且包含：
```
OPENAI_API_KEY=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
OPENAI_BASE_URL=https://openrouter.ai/api/v1
```

### 4. 运行诊断
```bash
# 运行完整诊断
python test_openrouter.py

# 运行模块测试
python test_modules.py

# 运行功能演示
python demo.py
```

## 📋 使用步骤

### 第一步：环境准备
1. 确保微信PC版已打开并登录
2. 不要最小化微信窗口
3. 进入任意聊天对话

### 第二步：启动程序
1. 双击 `启动程序.bat`
2. 选择 "2. 🧪 功能测试工具"
3. 在测试工具中点击各个测试按钮
4. 确认所有测试都通过

### 第三步：启动主程序
1. 在测试工具中点击 "🚀 启动主程序"
2. 或重新运行 `启动程序.bat` 选择选项1
3. 在主程序中点击 "开始监控"

### 第四步：测试功能
1. 让朋友给您发送测试消息
2. 观察程序是否自动回复
3. 查看消息历史记录

## ⚠️ 常见问题解决

### 问题1：找不到微信窗口
**解决方案：**
- 确保微信已打开且未最小化
- 检查微信窗口标题是否为"微信"
- 尝试重新启动微信

### 问题2：OCR识别失败
**解决方案：**
- 程序会自动使用备选方案
- 可以忽略PaddleOCR的警告
- 基本功能不受影响

### 问题3：API连接失败
**解决方案：**
- 检查网络连接
- 运行 `python test_openrouter.py` 测试
- 确认API密钥正确

### 问题4：程序无响应
**解决方案：**
- 使用Ctrl+C终止程序
- 重新启动
- 检查错误日志

## 🎯 功能测试清单

在使用前，请确认以下功能正常：

- [ ] ✅ 模块导入测试通过
- [ ] ✅ 配置文件测试通过  
- [ ] ✅ 微信窗口检测成功
- [ ] ✅ API连接测试通过
- [ ] ✅ 主程序可以启动
- [ ] ✅ 配置管理器可以打开

## 📞 获取帮助

### 自助诊断
1. 运行 `python simple_test.py` 查看详细测试结果
2. 查看 `wechat_ai.log` 日志文件
3. 运行 `python demo.py` 查看功能演示

### 配置管理
- 运行 `python config_manager.py` 打开配置界面
- 在配置管理器中测试API连接
- 调整OCR引擎和其他参数

### 文档资源
- `README.md` - 详细使用说明
- `项目结构说明.md` - 技术文档
- `快速启动指南.md` - 快速上手
- `openrouter_info.html` - API配置说明

## 🎉 成功启动后的使用提示

1. **首次使用**：建议先在测试群或个人对话中测试
2. **监控状态**：注意观察程序状态和日志输出
3. **手动干预**：随时可以手动发送回复或停止监控
4. **安全使用**：遵守微信使用条款，避免频繁操作

## 🔄 更新和维护

- 定期检查API余额和使用情况
- 根据需要调整回复频率和内容
- 备份重要配置文件
- 关注程序更新和改进

---

**记住：您的程序已经配置完成，所有测试都通过了！现在只需要按照推荐的启动方式使用即可。** 🎊
