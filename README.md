# 微信自动回复智能体

基于UI自动化和OCR技术的微信自动回复系统，支持固定窗口/区域截图回复，不影响其他区域办公。

## 功能特点

- 🤖 **智能回复**: 支持OpenAI GPT和本地AI模型
- 📸 **固定区域监控**: 只监控微信聊天窗口，不影响其他应用
- 🔍 **OCR文字识别**: 支持PaddleOCR和Tesseract双引擎
- 🎛️ **图形界面**: 提供友好的GUI控制界面
- ⚡ **实时监控**: 基于图像对比的高效消息检测
- 🛡️ **安全限制**: 频率限制、关键词过滤、人工接管
- 📝 **日志记录**: 完整的操作日志和错误追踪

## 技术架构

### 核心模块
- `window_controller.py` - 微信窗口控制和自动化操作
- `ocr_processor.py` - OCR文字识别和消息提取
- `ai_responder.py` - AI回复生成和对话管理
- `wechat_monitor.py` - 微信窗口监控主逻辑
- `main.py` - 主程序和GUI界面

### 技术栈
- **UI自动化**: pyautogui, pygetwindow
- **图像处理**: OpenCV, Pillow
- **OCR识别**: PaddleOCR (主要), Tesseract (备选)
- **AI接口**: OpenAI API, 本地AI支持
- **GUI界面**: Tkinter

## 安装部署

### 1. 环境要求
- Python 3.8+
- Windows 10/11 (主要支持)
- 微信PC版

### 2. 安装依赖
```bash
# 克隆项目
git clone <repository_url>
cd WeChatAI

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 配置设置
```bash
# 复制环境变量配置文件
copy .env.example .env

# 编辑 .env 文件，填入你的API密钥
notepad .env
```

### 4. 运行程序
```bash
# 启动GUI版本
python main.py

# 或启动命令行版本（修改config.py中SHOW_GUI=False）
python main.py
```

## 使用说明

### 基本使用流程

1. **启动程序**: 运行 `python main.py`
2. **打开微信**: 确保微信PC版已登录并打开
3. **开始监控**: 点击"开始监控"按钮
4. **查看状态**: 观察界面状态信息和消息历史
5. **手动干预**: 可随时发送手动回复或停止监控

### 配置说明

主要配置项在 `config.py` 文件中：

```python
# 微信窗口配置
WECHAT_WINDOW_TITLE = "微信"  # 微信窗口标题

# OCR配置
OCR_ENGINE = "paddleocr"  # OCR引擎选择
OCR_LANG = "ch"          # 识别语言

# AI配置
AI_PROVIDER = "openai"    # AI提供商
OPENAI_MODEL = "gpt-3.5-turbo"  # 使用的模型

# 监控配置
SCREENSHOT_INTERVAL = 2.0      # 截图间隔(秒)
AUTO_REPLY_ENABLED = True      # 是否启用自动回复
REPLY_DELAY = (1, 3)          # 回复延迟范围(秒)

# 安全配置
MAX_MESSAGES_PER_MINUTE = 10   # 每分钟最大回复数
IGNORE_KEYWORDS = ["系统消息", "撤回了一条消息"]  # 忽略关键词
```

### AI配置

#### OpenRouter AI配置（推荐，已配置）
OpenRouter是一个AI模型聚合平台，支持多种先进模型：
```
OPENAI_API_KEY=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
OPENAI_BASE_URL=https://openrouter.ai/api/v1
```

**支持的热门模型：**
- `anthropic/claude-3.5-sonnet` (推荐，已配置)
- `openai/gpt-4-turbo`
- `openai/gpt-3.5-turbo`
- `meta-llama/llama-3-8b-instruct`
- `google/gemini-pro`

#### OpenAI官方API配置
在 `.env` 文件中设置：
```
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
```

#### 本地AI配置
支持Ollama等本地AI服务：
```python
# config.py中修改
AI_PROVIDER = "local"
LOCAL_AI_URL = "http://localhost:11434/api/generate"
LOCAL_AI_MODEL = "qwen:7b"
```

## 工作原理

### 1. 窗口监控
- 自动检测微信窗口位置和大小
- 定期截取聊天区域图像
- 通过图像对比检测界面变化

### 2. 消息识别
- 使用OCR技术识别聊天内容
- 智能过滤系统消息和无效内容
- 提取最新的有效用户消息

### 3. 智能回复
- 调用AI API生成回复内容
- 维护对话历史上下文
- 支持多种回复策略和备用方案

### 4. 自动发送
- 模拟键盘输入发送回复
- 随机延迟模拟人工操作
- 支持频率限制和安全检查

## 安全特性

- **频率限制**: 防止回复过于频繁
- **关键词过滤**: 避免回复敏感内容
- **人工接管**: 支持随时手动干预
- **窗口检测**: 确保只在微信窗口活动时工作
- **错误恢复**: 自动处理异常情况

## 注意事项

1. **合规使用**: 请遵守微信使用条款，仅用于个人学习和研究
2. **隐私保护**: 程序不会上传或存储聊天内容
3. **稳定性**: 微信界面更新可能影响识别准确性
4. **性能影响**: 监控过程会占用一定系统资源
5. **网络依赖**: AI回复功能需要稳定的网络连接

## 故障排除

### 常见问题

1. **找不到微信窗口**
   - 确保微信已打开且未最小化
   - 检查窗口标题是否为"微信"

2. **OCR识别率低**
   - 调整微信窗口大小和位置
   - 确保聊天区域清晰可见
   - 尝试切换OCR引擎

3. **AI回复失败**
   - 检查API密钥配置
   - 确认网络连接正常
   - 查看日志文件排查错误

4. **发送消息失败**
   - 确保微信窗口处于前台
   - 检查输入框位置估算是否准确
   - 尝试手动点击输入框

### 日志查看
程序运行日志保存在 `wechat_ai.log` 文件中，可以查看详细的错误信息和运行状态。

## 开发扩展

### 添加新的OCR引擎
在 `ocr_processor.py` 中添加新的识别方法：
```python
def extract_text_new_engine(self, image: Image.Image) -> List[str]:
    # 实现新的OCR引擎
    pass
```

### 添加新的AI提供商
在 `ai_responder.py` 中添加新的回复生成方法：
```python
def _generate_new_ai_reply(self, message: str, contact_name: str) -> Optional[str]:
    # 实现新的AI接口
    pass
```

### 自定义消息过滤
修改 `ocr_processor.py` 中的 `_is_valid_message` 方法来自定义消息过滤逻辑。

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。使用时请遵守相关法律法规和平台服务条款。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的微信消息监控和自动回复
- 提供GUI控制界面
- 支持多种OCR引擎和AI提供商
