"""
简化测试程序 - 测试基本功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        import pyautogui
        print("✅ pyautogui 导入成功")
    except ImportError as e:
        print(f"❌ pyautogui 导入失败: {e}")
        return False
    
    try:
        import pygetwindow
        print("✅ pygetwindow 导入成功")
    except ImportError as e:
        print(f"❌ pygetwindow 导入失败: {e}")
        return False
    
    try:
        import cv2
        print("✅ opencv 导入成功")
    except ImportError as e:
        print(f"❌ opencv 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL 导入成功")
    except ImportError as e:
        print(f"❌ PIL 导入失败: {e}")
        return False
    
    try:
        import openai
        print("✅ openai 导入成功")
    except ImportError as e:
        print(f"❌ openai 导入失败: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ dotenv 导入成功")
    except ImportError as e:
        print(f"❌ dotenv 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n🔧 测试配置...")
    
    if not os.path.exists(".env"):
        print("❌ .env 文件不存在")
        return False
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key:
            print(f"✅ API Key 已配置: {api_key[:20]}...")
        else:
            print("❌ API Key 未配置")
            return False
        
        base_url = os.getenv("OPENAI_BASE_URL")
        if base_url:
            print(f"✅ Base URL 已配置: {base_url}")
        else:
            print("❌ Base URL 未配置")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_window_detection():
    """测试窗口检测"""
    print("\n🔍 测试窗口检测...")
    
    try:
        import pygetwindow as gw
        
        # 获取所有窗口
        windows = gw.getAllWindows()
        print(f"✅ 找到 {len(windows)} 个窗口")
        
        # 查找微信窗口
        wechat_windows = gw.getWindowsWithTitle("微信")
        if wechat_windows:
            print(f"✅ 找到 {len(wechat_windows)} 个微信窗口")
            for i, window in enumerate(wechat_windows):
                print(f"   {i+1}. {window.title} - {window.width}x{window.height}")
            return True
        else:
            print("⚠️ 未找到微信窗口（请确保微信已打开）")
            return False
            
    except Exception as e:
        print(f"❌ 窗口检测失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🌐 测试API连接...")
    
    try:
        import requests
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试连接
        response = requests.get(f"{base_url}/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            model_count = len(models.get("data", []))
            print(f"✅ API连接成功，找到 {model_count} 个模型")
            return True
        else:
            print(f"❌ API连接失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

class SimpleTestGUI:
    """简化测试GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信AI - 功能测试")
        self.root.geometry("600x500")
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 标题
        title_label = ttk.Label(self.root, text="🤖 微信自动回复智能体", font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=20)
        
        # 状态显示
        self.status_text = tk.Text(self.root, height=20, width=70, font=("Consolas", 9))
        self.status_text.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # 测试按钮
        ttk.Button(button_frame, text="🧪 测试导入", command=self.run_import_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔧 测试配置", command=self.run_config_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔍 测试窗口", command=self.run_window_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🌐 测试API", command=self.run_api_test).pack(side=tk.LEFT, padx=5)
        
        # 控制按钮
        control_frame = ttk.Frame(self.root)
        control_frame.pack(pady=5)
        
        ttk.Button(control_frame, text="🚀 启动主程序", command=self.start_main).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="🔧 配置管理", command=self.open_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="❌ 退出", command=self.root.quit).pack(side=tk.LEFT, padx=5)
        
        # 初始信息
        self.log("🎉 微信自动回复智能体测试工具")
        self.log("=" * 50)
        self.log("请点击测试按钮检查各项功能")
    
    def log(self, message):
        """添加日志"""
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def run_import_test(self):
        """运行导入测试"""
        self.log("\n🧪 开始测试模块导入...")
        if test_imports():
            self.log("✅ 所有模块导入成功！")
        else:
            self.log("❌ 部分模块导入失败")
    
    def run_config_test(self):
        """运行配置测试"""
        self.log("\n🔧 开始测试配置...")
        if test_config():
            self.log("✅ 配置测试通过！")
        else:
            self.log("❌ 配置测试失败")
    
    def run_window_test(self):
        """运行窗口测试"""
        self.log("\n🔍 开始测试窗口检测...")
        if test_window_detection():
            self.log("✅ 窗口检测成功！")
        else:
            self.log("⚠️ 窗口检测有问题")
    
    def run_api_test(self):
        """运行API测试"""
        self.log("\n🌐 开始测试API连接...")
        if test_api_connection():
            self.log("✅ API连接成功！")
        else:
            self.log("❌ API连接失败")
    
    def start_main(self):
        """启动主程序"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "main.py"])
            self.log("🚀 主程序已启动")
        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")
    
    def open_config(self):
        """打开配置管理"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "config_manager.py"])
            self.log("🔧 配置管理器已启动")
        except Exception as e:
            self.log(f"❌ 启动配置管理器失败: {e}")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🤖 微信自动回复智能体 - 简化测试")
    print("=" * 50)
    
    # 运行基本测试
    success_count = 0
    total_tests = 4
    
    if test_imports():
        success_count += 1
    
    if test_config():
        success_count += 1
    
    if test_window_detection():
        success_count += 1
    
    if test_api_connection():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count >= 3:
        print("✅ 基本功能正常，启动GUI测试工具...")
        app = SimpleTestGUI()
        app.run()
    else:
        print("❌ 基本测试失败较多，请检查环境配置")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
