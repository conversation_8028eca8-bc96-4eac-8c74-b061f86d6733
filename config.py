"""
微信自动回复智能体配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    # 微信窗口配置
    WECHAT_WINDOW_TITLE = "微信"  # 微信窗口标题
    MONITOR_REGION = None  # 监控区域，None表示自动检测微信窗口
    
    # OCR配置
    OCR_ENGINE = "paddleocr"  # 可选: paddleocr, tesseract
    OCR_LANG = "ch"  # 语言设置
    
    # AI回复配置
    AI_PROVIDER = "openai"  # 可选: openai, local
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    OPENAI_MODEL = "gpt-3.5-turbo"
    
    # 本地AI配置（如果使用本地模型）
    LOCAL_AI_URL = "http://localhost:11434/api/generate"  # Ollama默认地址
    LOCAL_AI_MODEL = "qwen:7b"
    
    # 监控配置
    SCREENSHOT_INTERVAL = 2.0  # 截图间隔（秒）
    MESSAGE_CHECK_INTERVAL = 1.0  # 消息检查间隔（秒）
    
    # 回复配置
    AUTO_REPLY_ENABLED = True  # 是否启用自动回复
    REPLY_DELAY = (1, 3)  # 回复延迟范围（秒）
    MAX_REPLY_LENGTH = 200  # 最大回复长度
    
    # 过滤配置
    IGNORE_KEYWORDS = ["系统消息", "撤回了一条消息", "[图片]", "[视频]", "[文件]"]
    IGNORE_GROUPS = []  # 忽略的群聊名称
    IGNORE_CONTACTS = []  # 忽略的联系人
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "wechat_ai.log"
    
    # 界面配置
    SHOW_GUI = True  # 是否显示GUI控制界面
    GUI_POSITION = (100, 100)  # GUI窗口位置
    
    # 安全配置
    MAX_MESSAGES_PER_MINUTE = 10  # 每分钟最大回复数量
    ENABLE_HUMAN_TAKEOVER = True  # 启用人工接管模式
    
    # 提示词配置
    SYSTEM_PROMPT = """你是一个友善、有帮助的AI助手。请根据用户的消息内容，给出简洁、有用的回复。
回复要求：
1. 保持友善和礼貌的语气
2. 回复内容要简洁明了，不超过100字
3. 如果是问候，要热情回应
4. 如果是问题，要尽力提供有用信息
5. 如果涉及敏感话题，要礼貌拒绝
6. 使用中文回复"""

# 创建配置实例
config = Config()
