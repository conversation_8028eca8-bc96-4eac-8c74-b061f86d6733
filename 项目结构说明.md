# 微信自动回复智能体 - 项目结构说明

## 项目文件结构

```
WeChatAI/
├── main.py                 # 主程序入口，包含GUI界面
├── wechat_monitor.py       # 微信窗口监控核心模块
├── window_controller.py    # 窗口控制和自动化操作
├── ocr_processor.py        # OCR文字识别处理
├── ai_responder.py         # AI回复生成模块
├── config.py              # 配置文件
├── utils.py               # 工具函数模块
├── test_modules.py        # 模块功能测试脚本
├── requirements.txt       # Python依赖包列表
├── .env.example          # 环境变量配置示例
├── .env                  # 环境变量配置文件（需要创建）
├── install.bat           # 安装脚本（Windows）
├── start.bat             # 启动脚本（Windows）
├── README.md             # 项目说明文档
├── 项目结构说明.md        # 本文件
└── logs/                 # 日志文件目录（自动创建）
    └── wechat_ai.log     # 运行日志
```

## 核心模块详解

### 1. main.py - 主程序
- **功能**: 程序入口，提供GUI控制界面
- **主要类**: `WeChatAIGUI`
- **关键功能**:
  - 图形用户界面
  - 监控状态控制
  - 消息历史显示
  - 手动回复功能
  - 截图预览

### 2. wechat_monitor.py - 监控核心
- **功能**: 微信窗口监控主逻辑
- **主要类**: `WeChatMonitor`
- **关键功能**:
  - 监控循环管理
  - 新消息检测
  - 消息处理流程
  - 自动回复控制
  - 频率限制

### 3. window_controller.py - 窗口控制
- **功能**: 微信窗口控制和自动化操作
- **主要类**: `WindowController`
- **关键功能**:
  - 微信窗口查找
  - 窗口截图
  - 聊天区域定位
  - 消息发送
  - 界面变化检测

### 4. ocr_processor.py - OCR处理
- **功能**: 文字识别和消息提取
- **主要类**: `OCRProcessor`
- **关键功能**:
  - 图像预处理
  - 文字识别（PaddleOCR/Tesseract）
  - 消息过滤
  - 内容清理

### 5. ai_responder.py - AI回复
- **功能**: AI回复生成和对话管理
- **主要类**: `AIResponder`
- **关键功能**:
  - OpenAI API调用
  - 本地AI支持
  - 对话历史管理
  - 回复策略控制
  - 备用回复

### 6. config.py - 配置管理
- **功能**: 全局配置管理
- **主要类**: `Config`
- **配置项**:
  - 微信窗口设置
  - OCR引擎配置
  - AI服务配置
  - 监控参数
  - 安全设置

### 7. utils.py - 工具函数
- **功能**: 通用工具函数
- **主要类**: `RateLimiter`, `ConfigManager`, `MessageCache`
- **工具函数**:
  - 文件操作
  - 时间处理
  - 文本处理
  - 重试机制
  - 速率限制

## 数据流程图

```
微信窗口 → 截图 → OCR识别 → 消息过滤 → AI生成回复 → 发送消息
    ↑         ↓         ↓          ↓           ↓
  窗口控制   图像处理   文字提取   智能判断    自动化操作
```

## 配置文件说明

### config.py 主要配置项

```python
# 微信窗口配置
WECHAT_WINDOW_TITLE = "微信"        # 微信窗口标题
MONITOR_REGION = None              # 监控区域

# OCR配置
OCR_ENGINE = "paddleocr"           # OCR引擎
OCR_LANG = "ch"                    # 识别语言

# AI配置
AI_PROVIDER = "openai"             # AI提供商
OPENAI_MODEL = "gpt-3.5-turbo"     # 模型名称

# 监控配置
SCREENSHOT_INTERVAL = 2.0          # 截图间隔
AUTO_REPLY_ENABLED = True          # 自动回复开关
REPLY_DELAY = (1, 3)              # 回复延迟范围

# 安全配置
MAX_MESSAGES_PER_MINUTE = 10       # 频率限制
IGNORE_KEYWORDS = [...]            # 忽略关键词
```

### .env 环境变量

```bash
# OpenAI配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地AI配置
LOCAL_AI_URL=http://localhost:11434/api/generate
LOCAL_AI_MODEL=qwen:7b
```

## 依赖包说明

### 核心依赖
- `pyautogui` - UI自动化
- `pygetwindow` - 窗口管理
- `opencv-python` - 图像处理
- `pillow` - 图像操作
- `paddleocr` - OCR识别
- `openai` - AI接口

### 可选依赖
- `pytesseract` - 备选OCR
- `pynput` - 输入控制
- `requests` - HTTP请求

## 运行流程

### 1. 初始化阶段
1. 加载配置文件
2. 初始化各个模块
3. 检查依赖和环境
4. 启动GUI界面

### 2. 监控阶段
1. 查找微信窗口
2. 定期截取聊天区域
3. 检测界面变化
4. 识别新消息内容

### 3. 处理阶段
1. OCR文字识别
2. 消息内容过滤
3. 判断是否需要回复
4. 生成AI回复内容

### 4. 回复阶段
1. 检查频率限制
2. 模拟键盘输入
3. 发送回复消息
4. 记录操作日志

## 扩展开发指南

### 添加新的OCR引擎
1. 在 `ocr_processor.py` 中添加新的识别方法
2. 更新 `_init_ocr_engine` 方法
3. 在配置中添加新引擎选项

### 添加新的AI提供商
1. 在 `ai_responder.py` 中添加新的回复生成方法
2. 更新 `_init_ai_client` 方法
3. 在配置中添加新提供商选项

### 自定义消息处理
1. 修改 `ocr_processor.py` 中的过滤逻辑
2. 调整 `ai_responder.py` 中的回复策略
3. 更新配置文件中的相关参数

## 调试和测试

### 运行测试
```bash
python test_modules.py
```

### 查看日志
- 日志文件: `wechat_ai.log`
- 日志级别: INFO, WARNING, ERROR
- 实时日志: GUI界面消息历史

### 常见问题排查
1. 检查微信窗口是否打开
2. 验证OCR引擎是否正常
3. 确认AI API配置正确
4. 查看日志文件错误信息

## 安全注意事项

1. **API密钥安全**: 不要将API密钥提交到版本控制
2. **频率限制**: 避免过于频繁的自动回复
3. **内容过滤**: 设置合适的关键词过滤
4. **人工监督**: 保持人工监督和干预能力
5. **合规使用**: 遵守微信使用条款和相关法规
