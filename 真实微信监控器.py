"""
真实微信监控器 - 真正能读取微信内容并自动回复
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pygetwindow as gw
import pyautogui
import pyperclip
import time
import threading
import requests
import json
import os
import re
from datetime import datetime
from dotenv import load_dotenv
from PIL import Image, ImageGrab
import cv2
import numpy as np

# 设置pyautogui
pyautogui.FAILSAFE = False
pyautogui.PAUSE = 0.1

# 加载环境变量
load_dotenv()

class RealWeChatMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("真实微信监控器")
        self.root.geometry("1000x700")
        
        # 核心变量
        self.target_window = None
        self.target_title = ""
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_messages = []
        
        # 坐标位置
        self.chat_area_coords = None
        self.input_box_coords = None
        self.send_button_coords = None
        
        # API配置
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎯 真实微信监控器", font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 步骤1：选择窗口
        step1_frame = ttk.LabelFrame(main_frame, text="步骤1：选择微信窗口", padding="10")
        step1_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step1_frame.columnconfigure(1, weight=1)
        
        ttk.Button(step1_frame, text="🔄 刷新", command=self.refresh_windows).grid(row=0, column=0, padx=(0, 10))
        
        self.window_var = tk.StringVar()
        self.window_combo = ttk.Combobox(step1_frame, textvariable=self.window_var, width=60, state="readonly")
        self.window_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(step1_frame, text="✅ 选择", command=self.select_window).grid(row=0, column=2)
        
        self.selected_label = ttk.Label(step1_frame, text="未选择窗口", foreground="red")
        self.selected_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 步骤2：定位坐标
        step2_frame = ttk.LabelFrame(main_frame, text="步骤2：定位微信元素", padding="10")
        step2_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(step2_frame, text="🎯 自动定位", command=self.auto_locate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step2_frame, text="📋 复制测试", command=self.test_copy_messages).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step2_frame, text="📝 输入测试", command=self.test_input).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step2_frame, text="📤 发送测试", command=self.test_send).pack(side=tk.LEFT, padx=(0, 10))
        
        self.locate_status = ttk.Label(step2_frame, text="未定位", foreground="red")
        self.locate_status.pack(side=tk.RIGHT)
        
        # 左侧：监控控制
        left_frame = ttk.LabelFrame(main_frame, text="监控控制", padding="10")
        left_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)
        
        # 控制按钮
        control_frame = ttk.Frame(left_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="🚀 开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ 停止", command=self.stop_monitoring, state="disabled")
        self.stop_btn.pack(side=tk.LEFT)
        
        # 监控日志
        self.log_text = scrolledtext.ScrolledText(left_frame, height=20, font=("Consolas", 9))
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：消息和回复
        right_frame = ttk.LabelFrame(main_frame, text="消息和回复", padding="10")
        right_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        right_frame.rowconfigure(3, weight=1)
        
        # 检测到的消息
        ttk.Label(right_frame, text="检测到的消息:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.message_text = scrolledtext.ScrolledText(right_frame, height=8, font=("微软雅黑", 9))
        self.message_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # AI回复
        ttk.Label(right_frame, text="AI回复:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.reply_text = scrolledtext.ScrolledText(right_frame, height=8, font=("微软雅黑", 9))
        self.reply_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 手动操作
        manual_frame = ttk.Frame(right_frame)
        manual_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(manual_frame, text="🤖 获取AI回复", command=self.manual_get_reply).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(manual_frame, text="📤 发送回复", command=self.manual_send_reply).pack(side=tk.LEFT)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        self.log("🎯 真实微信监控器已启动")
        self.log("使用说明：")
        self.log("1. 选择微信窗口")
        self.log("2. 点击'自动定位'定位微信元素")
        self.log("3. 测试各项功能")
        self.log("4. 开始监控")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
    
    def refresh_windows(self):
        """刷新微信窗口列表"""
        try:
            self.log("🔍 刷新微信窗口列表...")
            
            all_windows = gw.getAllWindows()
            wechat_windows = []
            
            for window in all_windows:
                title = window.title.strip()
                if ("微信" in title and 
                    len(title) > 0 and 
                    window.visible and 
                    window.width > 400 and 
                    window.height > 300):
                    wechat_windows.append(f"{title} ({window.width}x{window.height})")
            
            self.window_combo['values'] = wechat_windows
            
            if wechat_windows:
                self.log(f"✅ 找到 {len(wechat_windows)} 个微信窗口")
                if len(wechat_windows) == 1:
                    self.window_combo.current(0)
            else:
                self.log("❌ 未找到微信窗口")
                
        except Exception as e:
            self.log(f"❌ 刷新失败: {e}")
    
    def select_window(self):
        """选择目标窗口"""
        selected = self.window_var.get()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        try:
            self.target_title = selected.split(" (")[0]
            windows = gw.getWindowsWithTitle(self.target_title)
            
            if windows:
                self.target_window = windows[0]
                self.target_window.activate()
                time.sleep(0.5)
                
                self.selected_label.config(text=f"✅ 已选择: {self.target_title}", foreground="green")
                self.log(f"✅ 目标窗口: {self.target_title}")
                return True
            else:
                self.log(f"❌ 找不到窗口: {self.target_title}")
                return False
                
        except Exception as e:
            self.log(f"❌ 选择窗口失败: {e}")
            return False
    
    def auto_locate(self):
        """自动定位微信元素"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择窗口")
            return
        
        try:
            self.log("🎯 开始自动定位微信元素...")
            
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.5)
            
            # 获取窗口坐标
            left = self.target_window.left
            top = self.target_window.top
            width = self.target_window.width
            height = self.target_window.height
            
            self.log(f"窗口位置: ({left}, {top}), 大小: {width}x{height}")
            
            # 根据微信界面布局估算坐标
            # 聊天区域（右侧中间部分）
            chat_left = left + int(width * 0.25)
            chat_top = top + int(height * 0.15)
            chat_width = int(width * 0.7)
            chat_height = int(height * 0.6)
            self.chat_area_coords = (chat_left, chat_top, chat_width, chat_height)
            
            # 输入框（右下角）
            input_x = left + int(width * 0.6)
            input_y = top + int(height * 0.85)
            self.input_box_coords = (input_x, input_y)
            
            # 发送按钮（输入框右侧）
            send_x = left + int(width * 0.92)
            send_y = top + int(height * 0.85)
            self.send_button_coords = (send_x, send_y)
            
            self.log(f"聊天区域: {self.chat_area_coords}")
            self.log(f"输入框坐标: {self.input_box_coords}")
            self.log(f"发送按钮: {self.send_button_coords}")
            
            self.locate_status.config(text="✅ 已定位", foreground="green")
            self.log("✅ 自动定位完成")
            
        except Exception as e:
            self.log(f"❌ 自动定位失败: {e}")
    
    def test_copy_messages(self):
        """测试复制消息功能"""
        if not self.target_window or not self.chat_area_coords:
            messagebox.showwarning("警告", "请先选择窗口并定位")
            return
        
        try:
            self.log("📋 测试复制消息...")
            
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.5)
            
            # 点击聊天区域
            chat_x = self.chat_area_coords[0] + self.chat_area_coords[2] // 2
            chat_y = self.chat_area_coords[1] + self.chat_area_coords[3] // 2
            
            pyautogui.click(chat_x, chat_y)
            time.sleep(0.3)
            
            # 全选聊天内容
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.3)
            
            # 复制
            pyautogui.hotkey('ctrl', 'c')
            time.sleep(0.5)
            
            # 获取剪贴板内容
            clipboard_content = pyperclip.paste()
            
            if clipboard_content:
                self.log(f"✅ 复制成功，内容长度: {len(clipboard_content)}")
                
                # 显示部分内容
                preview = clipboard_content[:200] + "..." if len(clipboard_content) > 200 else clipboard_content
                self.message_text.delete(1.0, tk.END)
                self.message_text.insert(tk.END, f"复制的内容:\n{preview}")
                
                return clipboard_content
            else:
                self.log("❌ 复制失败，剪贴板为空")
                return None
                
        except Exception as e:
            self.log(f"❌ 复制消息失败: {e}")
            return None
    
    def test_input(self):
        """测试输入功能"""
        if not self.target_window or not self.input_box_coords:
            messagebox.showwarning("警告", "请先选择窗口并定位")
            return
        
        try:
            self.log("📝 测试输入功能...")
            
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.5)
            
            # 点击输入框
            pyautogui.click(self.input_box_coords[0], self.input_box_coords[1])
            time.sleep(0.3)
            
            # 清空输入框
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            pyautogui.press('delete')
            time.sleep(0.2)
            
            # 输入测试文本
            test_text = "这是一条测试消息"
            pyautogui.write(test_text, interval=0.02)
            time.sleep(0.5)
            
            self.log(f"✅ 输入测试成功: {test_text}")
            
        except Exception as e:
            self.log(f"❌ 输入测试失败: {e}")
    
    def test_send(self):
        """测试发送功能"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择窗口")
            return
        
        try:
            self.log("📤 测试发送功能...")
            
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.5)
            
            # 方法1: 按回车键发送
            pyautogui.press('enter')
            time.sleep(0.3)
            
            # 方法2: 如果有发送按钮坐标，点击发送按钮
            if self.send_button_coords:
                pyautogui.click(self.send_button_coords[0], self.send_button_coords[1])
                time.sleep(0.3)
            
            self.log("✅ 发送测试完成")
            
        except Exception as e:
            self.log(f"❌ 发送测试失败: {e}")
    
    def get_ai_reply(self, message):
        """获取AI回复"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "anthropic/claude-3.5-sonnet",
                "messages": [
                    {"role": "system", "content": "你是一个友善的AI助手，请用中文简洁回复，不超过50字。"},
                    {"role": "user", "content": message}
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = requests.post(f"{self.base_url}/chat/completions", 
                                   headers=headers, json=data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"].strip()
            else:
                self.log(f"❌ API调用失败: {response.status_code}")
                return "抱歉，我现在无法回复。"
                
        except Exception as e:
            self.log(f"❌ 获取AI回复失败: {e}")
            return "收到您的消息了。"
    
    def send_message(self, message):
        """发送消息到微信"""
        try:
            # 激活窗口
            self.target_window.activate()
            time.sleep(0.3)
            
            # 点击输入框
            pyautogui.click(self.input_box_coords[0], self.input_box_coords[1])
            time.sleep(0.2)
            
            # 清空输入框
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            pyautogui.press('delete')
            time.sleep(0.1)
            
            # 输入消息
            pyautogui.write(message, interval=0.01)
            time.sleep(0.3)
            
            # 发送消息
            pyautogui.press('enter')
            time.sleep(0.2)
            
            return True
            
        except Exception as e:
            self.log(f"❌ 发送消息失败: {e}")
            return False
    
    def manual_get_reply(self):
        """手动获取AI回复"""
        message_content = self.message_text.get(1.0, tk.END).strip()
        if not message_content:
            messagebox.showwarning("警告", "没有消息内容")
            return
        
        self.log("🤖 正在获取AI回复...")
        self.reply_text.delete(1.0, tk.END)
        self.reply_text.insert(tk.END, "正在获取AI回复...")
        
        def get_reply():
            reply = self.get_ai_reply(message_content)
            self.reply_text.delete(1.0, tk.END)
            self.reply_text.insert(tk.END, reply)
            self.log(f"✅ AI回复: {reply}")
        
        threading.Thread(target=get_reply, daemon=True).start()
    
    def manual_send_reply(self):
        """手动发送回复"""
        reply_content = self.reply_text.get(1.0, tk.END).strip()
        if not reply_content:
            messagebox.showwarning("警告", "没有回复内容")
            return
        
        if not self.input_box_coords:
            messagebox.showwarning("警告", "请先定位输入框")
            return
        
        self.log(f"📤 发送回复: {reply_content}")
        
        if self.send_message(reply_content):
            self.log("✅ 回复发送成功")
        else:
            self.log("❌ 回复发送失败")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.target_window or not self.chat_area_coords or not self.input_box_coords:
            messagebox.showwarning("警告", "请先完成窗口选择和定位")
            return
        
        self.is_monitoring = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        self.log("🚀 开始监控微信对话...")
        self.status_var.set("监控中...")
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        self.log("⏹️ 监控已停止")
        self.status_var.set("已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 复制聊天内容
                current_messages = self.test_copy_messages()
                
                if current_messages and current_messages != self.last_messages:
                    self.log("📨 检测到新消息")
                    
                    # 提取最新消息（简单处理）
                    lines = current_messages.strip().split('\n')
                    if lines:
                        latest_line = lines[-1].strip()
                        
                        # 过滤掉系统消息和自己的消息
                        if (latest_line and 
                            not latest_line.startswith('[') and 
                            not latest_line.endswith(']') and
                            len(latest_line) > 1):
                            
                            self.log(f"新消息: {latest_line}")
                            
                            # 显示消息
                            self.message_text.delete(1.0, tk.END)
                            self.message_text.insert(tk.END, latest_line)
                            
                            # 获取AI回复
                            ai_reply = self.get_ai_reply(latest_line)
                            
                            if ai_reply:
                                self.log(f"AI回复: {ai_reply}")
                                
                                # 显示回复
                                self.reply_text.delete(1.0, tk.END)
                                self.reply_text.insert(tk.END, ai_reply)
                                
                                # 发送回复
                                if self.send_message(ai_reply):
                                    self.log("✅ 自动回复发送成功")
                                else:
                                    self.log("❌ 自动回复发送失败")
                    
                    self.last_messages = current_messages
                
                time.sleep(3)  # 每3秒检查一次
                
            except Exception as e:
                self.log(f"❌ 监控出错: {e}")
                time.sleep(5)
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    app = RealWeChatMonitor()
    app.run()

if __name__ == "__main__":
    main()
