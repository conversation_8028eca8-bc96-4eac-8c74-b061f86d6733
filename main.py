"""
微信自动回复智能体主程序
"""
import sys
import time
import logging
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime
from PIL import Image, ImageTk

from wechat_monitor import WeChatMonitor
from config import config

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class WeChatAIGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信自动回复智能体")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置窗口位置
        if config.GUI_POSITION:
            x, y = config.GUI_POSITION
            self.root.geometry(f"+{x}+{y}")

        self.monitor = WeChatMonitor()
        self.setup_ui()
        self.setup_callbacks()

        # 状态变量
        self.is_monitoring = False
        self.message_history = []

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="5")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 开始/停止按钮
        self.start_button = ttk.Button(control_frame, text="开始监控", command=self.toggle_monitoring)
        self.start_button.grid(row=0, column=0, padx=(0, 10))

        # 状态标签
        self.status_label = ttk.Label(control_frame, text="状态: 未启动", foreground="red")
        self.status_label.grid(row=0, column=1, padx=(0, 10))

        # 截图按钮
        screenshot_button = ttk.Button(control_frame, text="截图预览", command=self.take_screenshot)
        screenshot_button.grid(row=0, column=2, padx=(0, 10))

        # 设置按钮
        settings_button = ttk.Button(control_frame, text="⚙️ 设置", command=self.open_settings)
        settings_button.grid(row=0, column=3, padx=(0, 10))

        # 配置管理按钮
        config_button = ttk.Button(control_frame, text="🔧 API配置", command=self.open_config_manager)
        config_button.grid(row=0, column=4)

        # 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="运行信息", padding="5")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)

        # 信息标签
        ttk.Label(info_frame, text="OCR引擎:").grid(row=0, column=0, sticky=tk.W)
        self.ocr_label = ttk.Label(info_frame, text=config.OCR_ENGINE)
        self.ocr_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(info_frame, text="AI提供商:").grid(row=1, column=0, sticky=tk.W)
        self.ai_label = ttk.Label(info_frame, text=config.AI_PROVIDER)
        self.ai_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(info_frame, text="微信窗口:").grid(row=2, column=0, sticky=tk.W)
        self.window_label = ttk.Label(info_frame, text="未检测", foreground="red")
        self.window_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))

        # 消息历史面板
        history_frame = ttk.LabelFrame(main_frame, text="消息历史", padding="5")
        history_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        history_frame.columnconfigure(0, weight=1)
        history_frame.rowconfigure(0, weight=1)

        # 消息历史文本框
        self.history_text = scrolledtext.ScrolledText(
            history_frame,
            wrap=tk.WORD,
            width=70,
            height=15,
            font=("微软雅黑", 9)
        )
        self.history_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 手动回复面板
        reply_frame = ttk.LabelFrame(main_frame, text="手动回复", padding="5")
        reply_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        reply_frame.columnconfigure(0, weight=1)

        # 回复输入框
        self.reply_entry = ttk.Entry(reply_frame, font=("微软雅黑", 9))
        self.reply_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        self.reply_entry.bind('<Return>', self.send_manual_reply)

        # 发送按钮
        send_button = ttk.Button(reply_frame, text="发送", command=self.send_manual_reply)
        send_button.grid(row=0, column=1)

        # 启动状态更新定时器
        self.update_status()

    def setup_callbacks(self):
        """设置监控回调函数"""
        self.monitor.set_callbacks(
            on_message_received=self.on_message_received,
            on_reply_sent=self.on_reply_sent,
            on_error=self.on_error
        )

    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.is_monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()

    def start_monitoring(self):
        """开始监控"""
        try:
            self.monitor.start_monitoring()
            self.is_monitoring = True
            self.start_button.config(text="停止监控")
            self.status_label.config(text="状态: 监控中", foreground="green")
            self.add_log("开始监控微信窗口")

        except Exception as e:
            messagebox.showerror("错误", f"启动监控失败: {e}")
            logger.error(f"启动监控失败: {e}")

    def stop_monitoring(self):
        """停止监控"""
        try:
            self.monitor.stop_monitoring()
            self.is_monitoring = False
            self.start_button.config(text="开始监控")
            self.status_label.config(text="状态: 已停止", foreground="red")
            self.add_log("停止监控微信窗口")

        except Exception as e:
            messagebox.showerror("错误", f"停止监控失败: {e}")
            logger.error(f"停止监控失败: {e}")

    def take_screenshot(self):
        """截图预览"""
        try:
            screenshot = self.monitor.get_current_screenshot()
            if screenshot:
                self.show_screenshot_window(screenshot)
            else:
                messagebox.showwarning("警告", "无法获取截图，请确保微信窗口已打开")

        except Exception as e:
            messagebox.showerror("错误", f"截图失败: {e}")
            logger.error(f"截图失败: {e}")

    def show_screenshot_window(self, image: Image.Image):
        """显示截图窗口"""
        screenshot_window = tk.Toplevel(self.root)
        screenshot_window.title("微信窗口截图")
        screenshot_window.geometry("600x400")

        # 调整图片大小以适应窗口
        display_image = image.copy()
        display_image.thumbnail((580, 380), Image.Resampling.LANCZOS)

        # 转换为Tkinter格式
        photo = ImageTk.PhotoImage(display_image)

        # 显示图片
        label = ttk.Label(screenshot_window, image=photo)
        label.image = photo  # 保持引用
        label.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)

    def send_manual_reply(self, event=None):
        """发送手动回复"""
        message = self.reply_entry.get().strip()
        if not message:
            return

        try:
            if self.monitor.manual_reply(message):
                self.add_log(f"手动发送: {message}")
                self.reply_entry.delete(0, tk.END)
            else:
                messagebox.showerror("错误", "发送消息失败")

        except Exception as e:
            messagebox.showerror("错误", f"发送消息失败: {e}")
            logger.error(f"手动发送消息失败: {e}")

    def open_settings(self):
        """打开设置窗口"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("设置")
        settings_window.geometry("400x300")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 这里可以添加设置界面
        ttk.Label(settings_window, text="设置功能待实现", font=("微软雅黑", 12)).pack(
            expand=True, fill=tk.BOTH
        )

    def open_config_manager(self):
        """打开配置管理器"""
        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "config_manager.py"])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开配置管理器: {e}")

    def on_message_received(self, message: str):
        """消息接收回调"""
        self.add_log(f"收到消息: {message}")

    def on_reply_sent(self, original_message: str, reply: str):
        """回复发送回调"""
        self.add_log(f"自动回复: {reply}")

    def on_error(self, error_message: str):
        """错误回调"""
        self.add_log(f"错误: {error_message}")

    def add_log(self, message: str):
        """添加日志到界面"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        # 在主线程中更新UI
        self.root.after(0, self._update_history_text, log_message)

    def _update_history_text(self, message: str):
        """更新历史文本框"""
        self.history_text.insert(tk.END, message)
        self.history_text.see(tk.END)

        # 限制历史记录长度
        lines = self.history_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            # 删除前面的行
            self.history_text.delete("1.0", f"{len(lines)-800}.0")

    def update_status(self):
        """更新状态信息"""
        try:
            status = self.monitor.get_status()

            # 更新微信窗口状态
            if status.get("wechat_window_found"):
                self.window_label.config(text="已连接", foreground="green")
            else:
                self.window_label.config(text="未检测", foreground="red")

        except Exception as e:
            logger.error(f"更新状态失败: {e}")

        # 每秒更新一次
        self.root.after(1000, self.update_status)

    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        finally:
            if self.is_monitoring:
                self.monitor.stop_monitoring()

def main():
    """主函数"""
    logger.info("微信自动回复智能体启动")

    try:
        if config.SHOW_GUI:
            # 启动GUI模式
            app = WeChatAIGUI()
            app.run()
        else:
            # 启动命令行模式
            monitor = WeChatMonitor()
            monitor.start_monitoring()

            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("程序被用户中断")
            finally:
                monitor.stop_monitoring()

    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
