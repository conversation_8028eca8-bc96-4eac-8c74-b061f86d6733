"""
微信窗口诊断工具 - 精确检测微信聊天窗口
"""
import pygetwindow as gw
import pyautogui
from PIL import Image
import time

def find_real_wechat_window():
    """查找真正的微信聊天窗口"""
    print("🔍 正在查找微信窗口...")
    print("=" * 50)
    
    # 获取所有窗口
    all_windows = gw.getAllWindows()
    print(f"📊 系统中共有 {len(all_windows)} 个窗口")
    
    # 查找包含"微信"的窗口
    wechat_candidates = []
    for window in all_windows:
        title = window.title.strip()
        if "微信" in title and len(title) > 0:
            wechat_candidates.append(window)
    
    print(f"\n🔍 找到 {len(wechat_candidates)} 个包含'微信'的窗口:")
    
    real_wechat = None
    for i, window in enumerate(wechat_candidates):
        print(f"\n{i+1}. 窗口信息:")
        print(f"   标题: {window.title}")
        print(f"   位置: ({window.left}, {window.top})")
        print(f"   大小: {window.width} x {window.height}")
        print(f"   状态: {'最小化' if window.isMinimized else '正常'}")
        print(f"   可见: {window.visible}")
        
        # 判断是否为真正的微信窗口
        if (window.title == "微信" and 
            window.width > 800 and 
            window.height > 600 and 
            not window.isMinimized and
            window.visible):
            real_wechat = window
            print(f"   ✅ 这可能是真正的微信聊天窗口")
        else:
            print(f"   ⚠️ 这不是目标微信窗口")
    
    return real_wechat

def test_window_screenshot(window):
    """测试窗口截图"""
    print(f"\n📸 测试窗口截图...")
    print("-" * 30)
    
    try:
        # 激活窗口
        window.activate()
        time.sleep(1)
        
        # 截取窗口
        left, top, width, height = window.left, window.top, window.width, window.height
        screenshot = pyautogui.screenshot(region=(left, top, width, height))
        
        # 保存截图
        screenshot.save("微信窗口截图.png")
        print(f"✅ 截图成功，已保存为 '微信窗口截图.png'")
        print(f"📏 截图尺寸: {screenshot.size}")
        
        # 分析截图区域
        analyze_screenshot_regions(screenshot, window)
        
        return True
        
    except Exception as e:
        print(f"❌ 截图失败: {e}")
        return False

def analyze_screenshot_regions(screenshot, window):
    """分析截图区域"""
    print(f"\n🔍 分析聊天区域...")
    print("-" * 30)
    
    width, height = screenshot.size
    
    # 估算各个区域
    regions = {
        "联系人列表": {
            "left": 0,
            "top": int(height * 0.1),
            "width": int(width * 0.25),
            "height": int(height * 0.8)
        },
        "聊天区域": {
            "left": int(width * 0.25),
            "top": int(height * 0.1),
            "width": int(width * 0.75),
            "height": int(height * 0.7)
        },
        "输入区域": {
            "left": int(width * 0.25),
            "top": int(height * 0.8),
            "width": int(width * 0.75),
            "height": int(height * 0.2)
        }
    }
    
    for region_name, region in regions.items():
        print(f"📍 {region_name}:")
        print(f"   位置: ({region['left']}, {region['top']})")
        print(f"   大小: {region['width']} x {region['height']}")
        
        # 截取区域
        try:
            left = region['left']
            top = region['top']
            right = left + region['width']
            bottom = top + region['height']
            
            region_img = screenshot.crop((left, top, right, bottom))
            filename = f"微信_{region_name}.png"
            region_img.save(filename)
            print(f"   ✅ 区域截图已保存: {filename}")
        except Exception as e:
            print(f"   ❌ 区域截图失败: {e}")

def test_message_detection():
    """测试消息检测"""
    print(f"\n💬 测试消息检测...")
    print("-" * 30)
    
    print("📝 请按以下步骤测试:")
    print("1. 确保微信窗口处于前台")
    print("2. 打开一个聊天对话")
    print("3. 让朋友发送一条测试消息")
    print("4. 观察程序是否能检测到变化")
    
    input("\n按回车键开始监控...")
    
    # 简单的变化检测测试
    window = find_real_wechat_window()
    if not window:
        print("❌ 未找到微信窗口")
        return
    
    print("🔄 开始监控变化（10秒）...")
    
    # 第一次截图
    try:
        window.activate()
        time.sleep(0.5)
        
        left, top, width, height = window.left, window.top, window.width, window.height
        
        # 聊天区域
        chat_left = left + int(width * 0.25)
        chat_top = top + int(height * 0.1)
        chat_width = int(width * 0.75)
        chat_height = int(height * 0.7)
        
        screenshot1 = pyautogui.screenshot(region=(chat_left, chat_top, chat_width, chat_height))
        screenshot1.save("监控前截图.png")
        print("📸 已保存监控前截图")
        
        # 等待变化
        print("⏳ 等待10秒，请在此期间发送测试消息...")
        time.sleep(10)
        
        # 第二次截图
        screenshot2 = pyautogui.screenshot(region=(chat_left, chat_top, chat_width, chat_height))
        screenshot2.save("监控后截图.png")
        print("📸 已保存监控后截图")
        
        # 简单对比
        if screenshot1.tobytes() != screenshot2.tobytes():
            print("✅ 检测到界面变化！")
            print("💡 这说明消息检测机制可以工作")
        else:
            print("⚠️ 未检测到界面变化")
            print("💡 可能没有新消息，或者检测区域不正确")
            
    except Exception as e:
        print(f"❌ 监控测试失败: {e}")

def test_message_sending():
    """测试消息发送"""
    print(f"\n📤 测试消息发送...")
    print("-" * 30)
    
    window = find_real_wechat_window()
    if not window:
        print("❌ 未找到微信窗口")
        return
    
    test_message = "这是一条测试消息，由AI自动发送"
    
    print(f"📝 准备发送测试消息: {test_message}")
    print("⚠️ 请确保当前聊天窗口是测试对象（如文件传输助手）")
    
    confirm = input("确认发送测试消息吗？(y/n): ")
    if confirm.lower() != 'y':
        print("❌ 用户取消发送")
        return
    
    try:
        # 激活微信窗口
        window.activate()
        time.sleep(0.5)
        
        # 计算输入框位置
        input_x = window.left + int(window.width * 0.6)
        input_y = window.top + int(window.height * 0.85)
        
        print(f"🎯 点击输入框位置: ({input_x}, {input_y})")
        
        # 点击输入框
        pyautogui.click(input_x, input_y)
        time.sleep(0.5)
        
        # 清空输入框
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        
        # 输入消息
        pyautogui.write(test_message, interval=0.05)
        time.sleep(0.5)
        
        # 发送消息
        pyautogui.press('enter')
        
        print("✅ 测试消息已发送")
        print("💡 请检查微信中是否收到消息")
        
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")

def main():
    """主函数"""
    print("🔧 微信窗口诊断工具")
    print("=" * 60)
    
    while True:
        print("\n请选择诊断项目:")
        print("1. 🔍 查找微信窗口")
        print("2. 📸 测试窗口截图")
        print("3. 💬 测试消息检测")
        print("4. 📤 测试消息发送")
        print("5. 🔄 完整诊断")
        print("6. ❌ 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            window = find_real_wechat_window()
            if window:
                print(f"\n✅ 找到目标微信窗口: {window.title}")
            else:
                print(f"\n❌ 未找到合适的微信窗口")
                print("💡 请确保:")
                print("   1. 微信PC版已打开")
                print("   2. 微信窗口未最小化")
                print("   3. 微信窗口标题为'微信'")
        
        elif choice == "2":
            window = find_real_wechat_window()
            if window:
                test_window_screenshot(window)
            else:
                print("❌ 请先确保微信窗口正常打开")
        
        elif choice == "3":
            test_message_detection()
        
        elif choice == "4":
            test_message_sending()
        
        elif choice == "5":
            print("\n🔄 开始完整诊断...")
            window = find_real_wechat_window()
            if window:
                test_window_screenshot(window)
                test_message_detection()
            else:
                print("❌ 微信窗口检测失败，无法继续")
        
        elif choice == "6":
            print("\n👋 诊断结束")
            break
        
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
