"""
微信自动回复智能体 - 快速设置向导
"""
import os
import sys
import subprocess
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 微信自动回复智能体 - 快速设置向导")
    print("=" * 60)
    print()

def check_python():
    """检查Python环境"""
    print("1️⃣ 检查Python环境...")
    
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
            return True
        else:
            print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
            print("   需要Python 3.8或更高版本")
            return False
    except Exception as e:
        print(f"❌ Python环境检查失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print("\n2️⃣ 安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        if not os.path.exists("requirements.txt"):
            print("❌ 未找到requirements.txt文件")
            return False
        
        print("正在安装依赖包，请稍候...")
        
        # 尝试使用pip安装
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print("❌ 依赖包安装失败，尝试使用国内镜像源...")
            
            # 尝试使用清华镜像源
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 依赖包安装成功（使用镜像源）")
                return True
            else:
                print(f"❌ 依赖包安装失败: {result.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def setup_config():
    """设置配置文件"""
    print("\n3️⃣ 设置配置文件...")
    
    try:
        # 检查.env文件
        if not os.path.exists(".env"):
            if os.path.exists(".env.example"):
                # 复制示例文件
                with open(".env.example", "r", encoding="utf-8") as f:
                    content = f.read()
                
                with open(".env", "w", encoding="utf-8") as f:
                    f.write(content)
                
                print("✅ 已创建.env配置文件")
            else:
                # 创建基本配置文件
                basic_config = """# 微信自动回复智能体配置文件
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地AI配置（可选）
# LOCAL_AI_URL=http://localhost:11434/api/generate
# LOCAL_AI_MODEL=qwen:7b
"""
                with open(".env", "w", encoding="utf-8") as f:
                    f.write(basic_config)
                
                print("✅ 已创建基本.env配置文件")
        else:
            print("✅ .env配置文件已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件设置失败: {e}")
        return False

def get_api_key():
    """获取API密钥配置"""
    print("\n4️⃣ 配置API密钥...")
    
    print("请选择AI服务提供商:")
    print("1. OpenAI GPT (推荐)")
    print("2. 本地AI模型 (如Ollama)")
    print("3. 跳过配置 (稍后手动配置)")
    
    while True:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            return setup_openai()
        elif choice == "2":
            return setup_local_ai()
        elif choice == "3":
            print("⏭️ 跳过API配置，请稍后手动编辑.env文件")
            return True
        else:
            print("❌ 无效选择，请重新输入")

def setup_openai():
    """设置OpenAI配置"""
    print("\n🔧 配置OpenAI API...")
    
    api_key = input("请输入OpenAI API Key (或按回车跳过): ").strip()
    
    if not api_key:
        print("⏭️ 跳过OpenAI配置")
        return True
    
    # 可选：自定义API地址
    print("\n是否使用自定义API地址? (如Azure OpenAI)")
    use_custom = input("输入 y 使用自定义地址，直接回车使用默认地址: ").strip().lower()
    
    base_url = "https://api.openai.com/v1"
    if use_custom == "y":
        custom_url = input("请输入自定义API地址: ").strip()
        if custom_url:
            base_url = custom_url
    
    try:
        # 更新.env文件
        update_env_file("OPENAI_API_KEY", api_key)
        update_env_file("OPENAI_BASE_URL", base_url)
        
        print("✅ OpenAI配置已保存")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI配置失败: {e}")
        return False

def setup_local_ai():
    """设置本地AI配置"""
    print("\n🔧 配置本地AI...")
    
    url = input("请输入本地AI服务地址 (默认: http://localhost:11434/api/generate): ").strip()
    if not url:
        url = "http://localhost:11434/api/generate"
    
    model = input("请输入模型名称 (默认: qwen:7b): ").strip()
    if not model:
        model = "qwen:7b"
    
    try:
        # 更新配置文件
        update_config_file("AI_PROVIDER", "local")
        update_env_file("LOCAL_AI_URL", url)
        update_env_file("LOCAL_AI_MODEL", model)
        
        print("✅ 本地AI配置已保存")
        return True
        
    except Exception as e:
        print(f"❌ 本地AI配置失败: {e}")
        return False

def update_env_file(key, value):
    """更新.env文件中的配置项"""
    if not os.path.exists(".env"):
        return
    
    # 读取现有内容
    with open(".env", "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    # 查找并更新配置项
    updated = False
    for i, line in enumerate(lines):
        if line.strip().startswith(f"{key}="):
            lines[i] = f"{key}={value}\n"
            updated = True
            break
    
    # 如果没找到，添加新配置项
    if not updated:
        lines.append(f"{key}={value}\n")
    
    # 写回文件
    with open(".env", "w", encoding="utf-8") as f:
        f.writelines(lines)

def update_config_file(key, value):
    """更新config.py文件中的配置项"""
    # 这里可以实现更复杂的配置文件更新逻辑
    # 暂时只是占位符
    pass

def test_installation():
    """测试安装"""
    print("\n5️⃣ 测试安装...")
    
    try:
        # 测试导入核心模块
        print("正在测试核心模块...")
        
        import config
        print("✅ 配置模块导入成功")
        
        from window_controller import WindowController
        print("✅ 窗口控制模块导入成功")
        
        from ocr_processor import OCRProcessor
        print("✅ OCR处理模块导入成功")
        
        from ai_responder import AIResponder
        print("✅ AI回复模块导入成功")
        
        print("✅ 所有核心模块测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("   请检查依赖包是否正确安装")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 设置完成！")
    print("=" * 60)
    
    print("\n📋 后续步骤:")
    print("1. 确保微信PC版已安装并登录")
    print("2. 运行 python main.py 启动程序")
    print("3. 或运行 python demo.py 查看功能演示")
    print("4. 或运行 python test_modules.py 进行详细测试")
    
    print("\n⚙️ 配置文件:")
    print("- .env: 环境变量配置（API密钥等）")
    print("- config.py: 主要功能配置")
    
    print("\n📚 帮助文档:")
    print("- README.md: 详细使用说明")
    print("- 项目结构说明.md: 项目结构介绍")
    
    print("\n🔧 如需修改配置:")
    print("- 编辑 .env 文件修改API配置")
    print("- 编辑 config.py 文件修改功能配置")

def main():
    """主函数"""
    print_header()
    
    success_steps = 0
    total_steps = 5
    
    # 执行设置步骤
    if check_python():
        success_steps += 1
    else:
        print("\n❌ Python环境检查失败，请安装Python 3.8+后重试")
        return
    
    if install_dependencies():
        success_steps += 1
    else:
        print("\n❌ 依赖包安装失败，请手动运行: pip install -r requirements.txt")
    
    if setup_config():
        success_steps += 1
    
    if get_api_key():
        success_steps += 1
    
    if test_installation():
        success_steps += 1
    
    # 显示结果
    print(f"\n📊 设置完成度: {success_steps}/{total_steps}")
    
    if success_steps == total_steps:
        show_next_steps()
    else:
        print("\n⚠️ 部分设置步骤失败，请检查错误信息并手动完成配置")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 设置被用户中断")
    except Exception as e:
        print(f"\n\n❌ 设置过程出错: {e}")
    finally:
        input("\n按回车键退出...")
