"""
微信窗口标题管理器 - 自定义监控窗口标题
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import pygetwindow as gw
from datetime import datetime
import threading
import time

class WindowTitleManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信窗口标题管理器")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 配置文件
        self.config_file = "window_titles.json"
        self.monitored_titles = self.load_config()
        
        # 当前检测到的窗口
        self.current_windows = []
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔧 微信窗口标题管理器", font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 左侧：当前窗口检测
        left_frame = ttk.LabelFrame(main_frame, text="🔍 当前微信窗口检测", padding="10")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)
        
        # 刷新按钮
        refresh_btn = ttk.Button(left_frame, text="🔄 刷新窗口列表", command=self.refresh_windows)
        refresh_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 窗口列表
        self.windows_tree = ttk.Treeview(left_frame, columns=("title", "size", "status"), show="tree headings")
        self.windows_tree.heading("#0", text="序号")
        self.windows_tree.heading("title", text="窗口标题")
        self.windows_tree.heading("size", text="窗口大小")
        self.windows_tree.heading("status", text="状态")
        
        self.windows_tree.column("#0", width=50)
        self.windows_tree.column("title", width=300)
        self.windows_tree.column("size", width=100)
        self.windows_tree.column("status", width=80)
        
        self.windows_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        windows_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=self.windows_tree.yview)
        windows_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.windows_tree.configure(yscrollcommand=windows_scrollbar.set)
        
        # 添加到监控按钮
        add_btn = ttk.Button(left_frame, text="➕ 添加到监控列表", command=self.add_selected_window)
        add_btn.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 右侧：监控配置
        right_frame = ttk.LabelFrame(main_frame, text="⚙️ 监控窗口配置", padding="10")
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 配置说明
        info_label = ttk.Label(right_frame, text="最多可配置10个监控窗口标题", font=("微软雅黑", 9))
        info_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # 监控列表框架
        monitor_frame = ttk.Frame(right_frame)
        monitor_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monitor_frame.columnconfigure(0, weight=1)
        monitor_frame.rowconfigure(0, weight=1)
        
        # 监控列表
        self.monitor_tree = ttk.Treeview(monitor_frame, columns=("title", "enabled", "last_seen"), show="tree headings")
        self.monitor_tree.heading("#0", text="序号")
        self.monitor_tree.heading("title", text="监控标题")
        self.monitor_tree.heading("enabled", text="启用")
        self.monitor_tree.heading("last_seen", text="最后检测")
        
        self.monitor_tree.column("#0", width=50)
        self.monitor_tree.column("title", width=250)
        self.monitor_tree.column("enabled", width=60)
        self.monitor_tree.column("last_seen", width=120)
        
        self.monitor_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 监控列表滚动条
        monitor_scrollbar = ttk.Scrollbar(monitor_frame, orient="vertical", command=self.monitor_tree.yview)
        monitor_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.monitor_tree.configure(yscrollcommand=monitor_scrollbar.set)
        
        # 监控操作按钮
        monitor_btn_frame = ttk.Frame(right_frame)
        monitor_btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(monitor_btn_frame, text="✏️ 编辑", command=self.edit_monitor_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(monitor_btn_frame, text="🔄 切换启用", command=self.toggle_monitor_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(monitor_btn_frame, text="❌ 删除", command=self.delete_monitor_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(monitor_btn_frame, text="➕ 手动添加", command=self.manual_add_title).pack(side=tk.LEFT)
        
        # 底部：操作和状态
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        bottom_frame.columnconfigure(1, weight=1)
        
        # 操作按钮
        ttk.Button(bottom_frame, text="💾 保存配置", command=self.save_config).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(bottom_frame, text="🧪 测试监控", command=self.test_monitoring).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(bottom_frame, text="🚀 启动主程序", command=self.start_main_program).grid(row=0, column=2, padx=(0, 10))
        ttk.Button(bottom_frame, text="❌ 关闭", command=self.root.quit).grid(row=0, column=3)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(bottom_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(10, 0))
        
        # 更新监控列表显示
        self.update_monitor_display()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('monitored_titles', [])
            return []
        except Exception as e:
            print(f"加载配置失败: {e}")
            return []
    
    def save_config(self):
        """保存配置文件"""
        try:
            config_data = {
                'monitored_titles': self.monitored_titles,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            self.status_var.set("✅ 配置已保存")
            messagebox.showinfo("成功", "配置已保存到 window_titles.json")
            
            # 同时更新主程序配置
            self.update_main_config()
            
        except Exception as e:
            self.status_var.set(f"❌ 保存失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def update_main_config(self):
        """更新主程序配置文件"""
        try:
            # 更新config.py中的窗口标题配置
            enabled_titles = [item['title'] for item in self.monitored_titles if item['enabled']]
            
            if enabled_titles:
                # 创建一个配置更新文件
                config_update = {
                    'WECHAT_WINDOW_TITLES': enabled_titles,
                    'PRIMARY_TITLE': enabled_titles[0] if enabled_titles else "微信"
                }
                
                with open('window_config.json', 'w', encoding='utf-8') as f:
                    json.dump(config_update, f, ensure_ascii=False, indent=2)
                
                print("主程序配置已更新")
        except Exception as e:
            print(f"更新主程序配置失败: {e}")
    
    def refresh_windows(self):
        """刷新窗口列表"""
        self.status_var.set("🔍 正在检测窗口...")
        self.root.update()
        
        try:
            # 清空当前列表
            for item in self.windows_tree.get_children():
                self.windows_tree.delete(item)
            
            # 获取所有窗口
            all_windows = gw.getAllWindows()
            
            # 筛选包含"微信"的窗口
            wechat_windows = []
            for window in all_windows:
                title = window.title.strip()
                if "微信" in title and len(title) > 0 and window.visible:
                    wechat_windows.append(window)
            
            self.current_windows = wechat_windows
            
            # 显示窗口列表
            for i, window in enumerate(wechat_windows):
                status = "正常" if not window.isMinimized else "最小化"
                size_text = f"{window.width}x{window.height}"
                
                self.windows_tree.insert("", "end", text=str(i+1), values=(
                    window.title,
                    size_text,
                    status
                ))
            
            self.status_var.set(f"✅ 找到 {len(wechat_windows)} 个微信窗口")
            
        except Exception as e:
            self.status_var.set(f"❌ 检测失败: {e}")
    
    def add_selected_window(self):
        """添加选中的窗口到监控列表"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        if len(self.monitored_titles) >= 10:
            messagebox.showwarning("警告", "最多只能监控10个窗口")
            return
        
        # 获取选中窗口的标题
        item = self.windows_tree.item(selection[0])
        window_title = item['values'][0]
        
        # 检查是否已存在
        for existing in self.monitored_titles:
            if existing['title'] == window_title:
                messagebox.showinfo("提示", "该窗口标题已在监控列表中")
                return
        
        # 添加到监控列表
        new_item = {
            'title': window_title,
            'enabled': True,
            'added_time': datetime.now().isoformat(),
            'last_seen': datetime.now().isoformat()
        }
        
        self.monitored_titles.append(new_item)
        self.update_monitor_display()
        self.status_var.set(f"✅ 已添加: {window_title}")
    
    def manual_add_title(self):
        """手动添加窗口标题"""
        if len(self.monitored_titles) >= 10:
            messagebox.showwarning("警告", "最多只能监控10个窗口")
            return
        
        # 创建输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("手动添加窗口标题")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))
        
        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="请输入要监控的窗口标题:").pack(pady=(0, 10))
        
        title_var = tk.StringVar()
        title_entry = ttk.Entry(frame, textvariable=title_var, width=40)
        title_entry.pack(pady=(0, 10))
        title_entry.focus()
        
        ttk.Label(frame, text="示例: 微信 - 张三, 微信 - 工作群", font=("微软雅黑", 8)).pack(pady=(0, 10))
        
        def add_title():
            title = title_var.get().strip()
            if not title:
                messagebox.showwarning("警告", "请输入窗口标题")
                return
            
            # 检查是否已存在
            for existing in self.monitored_titles:
                if existing['title'] == title:
                    messagebox.showinfo("提示", "该窗口标题已在监控列表中")
                    return
            
            # 添加到监控列表
            new_item = {
                'title': title,
                'enabled': True,
                'added_time': datetime.now().isoformat(),
                'last_seen': "未检测"
            }
            
            self.monitored_titles.append(new_item)
            self.update_monitor_display()
            self.status_var.set(f"✅ 已添加: {title}")
            dialog.destroy()
        
        def cancel():
            dialog.destroy()
        
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=(10, 0))
        
        ttk.Button(btn_frame, text="添加", command=add_title).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="取消", command=cancel).pack(side=tk.LEFT)
        
        # 绑定回车键
        title_entry.bind('<Return>', lambda e: add_title())
    
    def update_monitor_display(self):
        """更新监控列表显示"""
        # 清空列表
        for item in self.monitor_tree.get_children():
            self.monitor_tree.delete(item)
        
        # 添加监控项
        for i, item in enumerate(self.monitored_titles):
            enabled_text = "✅" if item['enabled'] else "❌"
            last_seen = item.get('last_seen', '未检测')
            if last_seen != '未检测' and last_seen != '从未':
                try:
                    dt = datetime.fromisoformat(last_seen)
                    last_seen = dt.strftime("%m-%d %H:%M")
                except:
                    pass
            
            self.monitor_tree.insert("", "end", text=str(i+1), values=(
                item['title'],
                enabled_text,
                last_seen
            ))
    
    def edit_monitor_item(self):
        """编辑监控项"""
        selection = self.monitor_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个监控项")
            return
        
        # 获取选中项的索引
        item_index = int(self.monitor_tree.item(selection[0])['text']) - 1
        current_item = self.monitored_titles[item_index]
        
        # 创建编辑对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑监控项")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="窗口标题:").pack(pady=(0, 5))
        
        title_var = tk.StringVar(value=current_item['title'])
        title_entry = ttk.Entry(frame, textvariable=title_var, width=40)
        title_entry.pack(pady=(0, 10))
        title_entry.focus()
        
        enabled_var = tk.BooleanVar(value=current_item['enabled'])
        ttk.Checkbutton(frame, text="启用监控", variable=enabled_var).pack(pady=(0, 10))
        
        def save_changes():
            new_title = title_var.get().strip()
            if not new_title:
                messagebox.showwarning("警告", "请输入窗口标题")
                return
            
            self.monitored_titles[item_index]['title'] = new_title
            self.monitored_titles[item_index]['enabled'] = enabled_var.get()
            
            self.update_monitor_display()
            self.status_var.set(f"✅ 已更新: {new_title}")
            dialog.destroy()
        
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=(10, 0))
        
        ttk.Button(btn_frame, text="保存", command=save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)
    
    def toggle_monitor_item(self):
        """切换监控项的启用状态"""
        selection = self.monitor_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个监控项")
            return
        
        item_index = int(self.monitor_tree.item(selection[0])['text']) - 1
        self.monitored_titles[item_index]['enabled'] = not self.monitored_titles[item_index]['enabled']
        
        self.update_monitor_display()
        status = "启用" if self.monitored_titles[item_index]['enabled'] else "禁用"
        self.status_var.set(f"✅ 已{status}: {self.monitored_titles[item_index]['title']}")
    
    def delete_monitor_item(self):
        """删除监控项"""
        selection = self.monitor_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个监控项")
            return
        
        item_index = int(self.monitor_tree.item(selection[0])['text']) - 1
        title = self.monitored_titles[item_index]['title']
        
        if messagebox.askyesno("确认", f"确定要删除监控项 '{title}' 吗？"):
            del self.monitored_titles[item_index]
            self.update_monitor_display()
            self.status_var.set(f"✅ 已删除: {title}")
    
    def test_monitoring(self):
        """测试监控功能"""
        if not self.monitored_titles:
            messagebox.showwarning("警告", "请先添加要监控的窗口标题")
            return
        
        # 创建测试窗口
        test_window = tk.Toplevel(self.root)
        test_window.title("监控测试")
        test_window.geometry("600x400")
        test_window.transient(self.root)
        
        frame = ttk.Frame(test_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="🧪 监控测试结果", font=("微软雅黑", 14, "bold")).pack(pady=(0, 10))
        
        result_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, height=20)
        result_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        def run_test():
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, "开始测试监控功能...\n\n")
            
            enabled_titles = [item for item in self.monitored_titles if item['enabled']]
            result_text.insert(tk.END, f"启用的监控标题数量: {len(enabled_titles)}\n\n")
            
            # 检测当前窗口
            all_windows = gw.getAllWindows()
            found_count = 0
            
            for item in enabled_titles:
                title = item['title']
                result_text.insert(tk.END, f"检测标题: {title}\n")
                
                found = False
                for window in all_windows:
                    if window.title == title and window.visible:
                        found = True
                        found_count += 1
                        result_text.insert(tk.END, f"  ✅ 找到窗口: {window.width}x{window.height}\n")
                        break
                
                if not found:
                    result_text.insert(tk.END, f"  ❌ 未找到窗口\n")
                
                result_text.insert(tk.END, "\n")
            
            result_text.insert(tk.END, f"测试完成！找到 {found_count}/{len(enabled_titles)} 个窗口\n")
            result_text.see(tk.END)
        
        ttk.Button(frame, text="🔄 重新测试", command=run_test).pack()
        
        # 自动运行一次测试
        run_test()
    
    def start_main_program(self):
        """启动主程序"""
        try:
            # 先保存配置
            self.save_config()
            
            # 启动主程序
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "main.py"])
            
            self.status_var.set("🚀 主程序已启动")
            messagebox.showinfo("成功", "主程序已启动，配置已自动应用")
            
        except Exception as e:
            self.status_var.set(f"❌ 启动失败: {e}")
            messagebox.showerror("错误", f"启动主程序失败: {e}")
    
    def run(self):
        """运行管理器"""
        self.root.mainloop()

def main():
    """主函数"""
    app = WindowTitleManager()
    app.run()

if __name__ == "__main__":
    main()
