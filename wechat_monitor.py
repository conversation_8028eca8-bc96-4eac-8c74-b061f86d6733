"""
微信窗口监控和截图模块
"""
import time
import threading
from typing import Optional, Callable, Dict, Any
from PIL import Image
import logging
from datetime import datetime, timedelta

from window_controller import WindowController
from ocr_processor import OCRProcessor
from ai_responder import AIResponder
from config import config

logger = logging.getLogger(__name__)

class WeChatMonitor:
    def __init__(self):
        self.window_controller = WindowController()
        self.ocr_processor = OCRProcessor(
            engine=config.OCR_ENGINE, 
            lang=config.OCR_LANG
        )
        self.ai_responder = AIResponder()
        
        self.is_monitoring = False
        self.monitor_thread = None
        self.previous_screenshot = None
        self.last_message = ""
        self.last_reply_time = datetime.now()
        self.message_count = 0
        self.message_timestamps = []
        
        # 回调函数
        self.on_message_received = None
        self.on_reply_sent = None
        self.on_error = None
    
    def start_monitoring(self):
        """开始监控微信窗口"""
        if self.is_monitoring:
            logger.warning("监控已在运行中")
            return
        
        logger.info("开始监控微信窗口...")
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        logger.info("停止监控微信窗口...")
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
    
    def _monitor_loop(self):
        """监控主循环"""
        logger.info("监控循环已启动")
        
        while self.is_monitoring:
            try:
                # 检查微信窗口
                if not self.window_controller.find_wechat_window():
                    logger.warning("未找到微信窗口，等待重试...")
                    time.sleep(5)
                    continue
                
                # 截取聊天区域
                current_screenshot = self.window_controller.capture_chat_area()
                if not current_screenshot:
                    logger.warning("截取聊天区域失败")
                    time.sleep(config.SCREENSHOT_INTERVAL)
                    continue
                
                # 检测新消息
                if self._detect_new_message(current_screenshot):
                    # 处理新消息
                    self._process_new_message(current_screenshot)
                
                # 更新上一次截图
                self.previous_screenshot = current_screenshot
                
                # 等待下次检查
                time.sleep(config.SCREENSHOT_INTERVAL)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                if self.on_error:
                    self.on_error(f"监控出错: {e}")
                time.sleep(5)
        
        logger.info("监控循环已停止")
    
    def _detect_new_message(self, current_screenshot: Image.Image) -> bool:
        """检测是否有新消息"""
        if not self.previous_screenshot:
            return True  # 第一次截图，认为有新消息
        
        try:
            # 使用图像对比检测变化
            change_area = self.window_controller.detect_new_message_area(
                current_screenshot, self.previous_screenshot
            )
            
            if change_area:
                logger.debug(f"检测到界面变化: {change_area}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检测新消息失败: {e}")
            return False
    
    def _process_new_message(self, screenshot: Image.Image):
        """处理新消息"""
        try:
            # OCR识别文字
            texts = self.ocr_processor.extract_text(screenshot)
            if not texts:
                logger.debug("未识别到文字内容")
                return
            
            # 获取最新消息
            latest_message = self.ocr_processor.get_latest_message(texts)
            if not latest_message:
                logger.debug("未找到有效消息")
                return
            
            # 检查是否为重复消息
            if latest_message == self.last_message:
                logger.debug("消息重复，跳过处理")
                return
            
            logger.info(f"收到新消息: {latest_message}")
            self.last_message = latest_message
            
            # 触发消息接收回调
            if self.on_message_received:
                self.on_message_received(latest_message)
            
            # 检查是否需要自动回复
            if config.AUTO_REPLY_ENABLED and self._should_auto_reply(latest_message):
                self._auto_reply(latest_message)
            
        except Exception as e:
            logger.error(f"处理新消息失败: {e}")
            if self.on_error:
                self.on_error(f"处理消息出错: {e}")
    
    def _should_auto_reply(self, message: str) -> bool:
        """判断是否应该自动回复"""
        # 检查频率限制
        if not self._check_rate_limit():
            logger.warning("回复频率超限，跳过自动回复")
            return False
        
        # 检查AI回复条件
        if not self.ai_responder.should_reply(message):
            return False
        
        # 检查人工接管模式
        if config.ENABLE_HUMAN_TAKEOVER:
            # 可以在这里添加人工接管的逻辑
            pass
        
        return True
    
    def _check_rate_limit(self) -> bool:
        """检查回复频率限制"""
        now = datetime.now()
        
        # 清理过期的时间戳
        self.message_timestamps = [
            ts for ts in self.message_timestamps 
            if now - ts < timedelta(minutes=1)
        ]
        
        # 检查是否超过限制
        if len(self.message_timestamps) >= config.MAX_MESSAGES_PER_MINUTE:
            return False
        
        return True
    
    def _auto_reply(self, message: str):
        """自动回复消息"""
        try:
            # 生成AI回复
            reply = self.ai_responder.generate_reply(message)
            
            if not reply:
                # 使用备用回复
                reply = self.ai_responder.get_fallback_reply(message)
                logger.warning("AI回复生成失败，使用备用回复")
            
            # 发送回复
            if self.window_controller.send_message(reply, config.REPLY_DELAY):
                logger.info(f"自动回复已发送: {reply}")
                
                # 记录回复时间
                self.last_reply_time = datetime.now()
                self.message_timestamps.append(self.last_reply_time)
                
                # 触发回复发送回调
                if self.on_reply_sent:
                    self.on_reply_sent(message, reply)
            else:
                logger.error("发送回复失败")
                if self.on_error:
                    self.on_error("发送回复失败")
            
        except Exception as e:
            logger.error(f"自动回复失败: {e}")
            if self.on_error:
                self.on_error(f"自动回复出错: {e}")
    
    def manual_reply(self, message: str) -> bool:
        """手动发送回复"""
        try:
            return self.window_controller.send_message(message, config.REPLY_DELAY)
        except Exception as e:
            logger.error(f"手动回复失败: {e}")
            return False
    
    def get_current_screenshot(self) -> Optional[Image.Image]:
        """获取当前聊天区域截图"""
        return self.window_controller.capture_chat_area()
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "wechat_window_found": self.window_controller.wechat_window is not None,
            "last_message": self.last_message,
            "last_reply_time": self.last_reply_time.isoformat() if self.last_reply_time else None,
            "message_count_last_minute": len(self.message_timestamps),
            "ocr_engine": self.ocr_processor.engine,
            "ai_provider": self.ai_responder.provider
        }
    
    def set_callbacks(self, on_message_received: Callable = None, 
                     on_reply_sent: Callable = None, 
                     on_error: Callable = None):
        """设置回调函数"""
        self.on_message_received = on_message_received
        self.on_reply_sent = on_reply_sent
        self.on_error = on_error
