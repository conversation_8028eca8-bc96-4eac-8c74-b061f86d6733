# 🔧 微信自动回复 - 微信设置指南

## ❌ 当前问题

**检测结果：未找到真正的微信PC版窗口**

目前只检测到程序自己的窗口，没有找到标题为"微信"的真正微信聊天应用。

## ✅ 解决方案

### 第一步：安装微信PC版

如果您还没有安装微信PC版：

1. **访问微信官网**
   - 打开浏览器访问：https://pc.weixin.qq.com/
   - 点击"立即下载"

2. **安装微信PC版**
   - 下载完成后运行安装包
   - 按照提示完成安装
   - 安装完成后启动微信

### 第二步：登录微信PC版

1. **启动微信应用**
   - 双击桌面微信图标
   - 或从开始菜单找到"微信"并启动

2. **扫码登录**
   - 使用手机微信扫描二维码
   - 在手机上确认登录
   - 等待PC版微信显示聊天界面

### 第三步：确认微信窗口设置

**重要：确保微信窗口符合以下要求**

✅ **窗口标题**
- 必须显示为"微信"（不是其他名称）
- 不能是"微信 - 某某群"或"微信 - 某某好友"

✅ **窗口状态**
- 窗口必须是正常大小（不要最小化）
- 窗口大小建议至少800x600像素
- 窗口应该可见（不要被其他窗口完全遮挡）

✅ **聊天界面**
- 确保显示聊天列表和聊天区域
- 建议打开一个聊天对话（如文件传输助手）

### 第四步：验证设置

完成上述设置后，运行验证：

```bash
# 运行微信窗口诊断
python 微信窗口诊断.py
# 选择 "1. 🔍 查找微信窗口"
```

**期望结果：**
- 应该找到标题为"微信"的窗口
- 窗口大小应该合理（如800x600以上）
- 窗口状态应该是"正常"

### 第五步：测试功能

1. **测试窗口截图**
   ```bash
   # 在诊断工具中选择 "2. 📸 测试窗口截图"
   ```

2. **测试消息发送**
   ```bash
   # 在诊断工具中选择 "4. 📤 测试消息发送"
   ```

3. **启动自动回复**
   ```bash
   # 运行主程序
   python main.py
   # 点击"开始监控"
   ```

## 🔍 常见问题排查

### 问题1：找不到微信窗口
**可能原因：**
- 微信PC版未安装或未启动
- 微信窗口被最小化
- 微信窗口标题不是"微信"

**解决方法：**
- 确保微信PC版已安装并启动
- 恢复微信窗口到正常大小
- 检查窗口标题栏

### 问题2：微信窗口标题不对
**可能原因：**
- 当前显示的是特定聊天窗口
- 微信版本问题

**解决方法：**
- 点击微信左上角返回主界面
- 确保显示聊天列表界面
- 更新到最新版本微信

### 问题3：程序检测到错误的窗口
**可能原因：**
- 有多个包含"微信"的窗口
- 程序自己的窗口被误识别

**解决方法：**
- 关闭不必要的微信相关窗口
- 确保只有一个真正的微信主窗口打开

## 📱 微信PC版下载链接

**官方下载地址：**
- 官网：https://pc.weixin.qq.com/
- 直接下载：https://dldir1.qq.com/weixin/Windows/WeChatSetup.exe

**系统要求：**
- Windows 7 或更高版本
- 至少2GB内存
- 至少500MB硬盘空间

## 🎯 设置完成后的使用流程

1. **确认微信PC版正常运行**
   - 窗口标题为"微信"
   - 显示聊天界面
   - 可以正常收发消息

2. **运行自动回复程序**
   ```bash
   python main.py
   ```

3. **开始监控**
   - 点击"开始监控"按钮
   - 确认状态显示"监控中"
   - 确认"微信窗口"显示"已连接"

4. **测试自动回复**
   - 让朋友发送测试消息
   - 观察程序是否自动回复
   - 检查回复内容是否合适

## 💡 使用建议

1. **首次使用**
   - 建议先在"文件传输助手"中测试
   - 确认功能正常后再用于其他聊天

2. **日常使用**
   - 保持微信窗口可见
   - 定期检查程序状态
   - 注意回复内容的合适性

3. **安全使用**
   - 设置合适的回复频率
   - 添加必要的关键词过滤
   - 保持人工监督

---

**完成以上设置后，您的微信自动回复智能体就可以正常工作了！** 🎉
