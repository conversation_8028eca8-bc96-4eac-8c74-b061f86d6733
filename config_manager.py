"""
配置管理页面 - 用于管理API设置和查看OpenRouter信息
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import webbrowser
import requests
import json
import os
from datetime import datetime
import threading

class ConfigManagerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信AI配置管理器")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 当前配置
        self.current_config = self.load_current_config()
        
        self.setup_ui()
        self.load_config_display()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🤖 微信AI配置管理器", font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # OpenRouter配置选项卡
        self.openrouter_frame = ttk.Frame(notebook)
        notebook.add(self.openrouter_frame, text="🌐 OpenRouter AI")
        self.setup_openrouter_tab()
        
        # 通用配置选项卡
        self.general_frame = ttk.Frame(notebook)
        notebook.add(self.general_frame, text="⚙️ 通用配置")
        self.setup_general_tab()
        
        # 模型测试选项卡
        self.test_frame = ttk.Frame(notebook)
        notebook.add(self.test_frame, text="🧪 模型测试")
        self.setup_test_tab()
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="💾 保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🔄 重新加载", command=self.reload_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🌐 打开OpenRouter", command=self.open_openrouter).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ 关闭", command=self.root.quit).pack(side=tk.RIGHT)
    
    def setup_openrouter_tab(self):
        """设置OpenRouter配置选项卡"""
        # 滚动框架
        canvas = tk.Canvas(self.openrouter_frame)
        scrollbar = ttk.Scrollbar(self.openrouter_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # OpenRouter信息
        info_frame = ttk.LabelFrame(scrollable_frame, text="📋 OpenRouter AI 信息", padding="10")
        info_frame.pack(fill="x", padx=10, pady=5)
        
        info_text = """
🌟 OpenRouter AI 是一个AI模型聚合平台，提供多种先进的AI模型访问：

✅ 支持的热门模型：
• Claude 3.5 Sonnet (Anthropic) - 推荐
• GPT-4 Turbo (OpenAI)
• GPT-3.5 Turbo (OpenAI)
• Llama 2/3 (Meta)
• Gemini Pro (Google)
• 以及更多模型...

💰 优势：
• 统一API接口，兼容OpenAI格式
• 灵活的计费方式
• 多模型选择
• 稳定的服务质量

🔗 官网：https://openrouter.ai/
        """
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT, font=("微软雅黑", 9)).pack(anchor="w")
        
        # API配置
        api_frame = ttk.LabelFrame(scrollable_frame, text="🔑 API 配置", padding="10")
        api_frame.pack(fill="x", padx=10, pady=5)
        
        # API Key
        ttk.Label(api_frame, text="API Key:").grid(row=0, column=0, sticky="w", pady=2)
        self.api_key_var = tk.StringVar(value=self.current_config.get("api_key", ""))
        api_key_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, width=60, show="*")
        api_key_entry.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        # 显示/隐藏API Key按钮
        self.show_key_var = tk.BooleanVar()
        show_key_cb = ttk.Checkbutton(api_frame, text="显示", variable=self.show_key_var, command=self.toggle_api_key_visibility)
        show_key_cb.grid(row=0, column=2, padx=(5, 0))
        self.api_key_entry = api_key_entry
        
        # Base URL
        ttk.Label(api_frame, text="Base URL:").grid(row=1, column=0, sticky="w", pady=2)
        self.base_url_var = tk.StringVar(value=self.current_config.get("base_url", "https://openrouter.ai/api/v1"))
        ttk.Entry(api_frame, textvariable=self.base_url_var, width=60).grid(row=1, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        # 模型选择
        ttk.Label(api_frame, text="模型:").grid(row=2, column=0, sticky="w", pady=2)
        self.model_var = tk.StringVar(value=self.current_config.get("model", "anthropic/claude-3.5-sonnet"))
        model_combo = ttk.Combobox(api_frame, textvariable=self.model_var, width=57)
        model_combo['values'] = [
            "anthropic/claude-3.5-sonnet",
            "anthropic/claude-3-haiku",
            "openai/gpt-4-turbo",
            "openai/gpt-3.5-turbo",
            "meta-llama/llama-3-8b-instruct",
            "google/gemini-pro",
            "mistralai/mixtral-8x7b-instruct"
        ]
        model_combo.grid(row=2, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        # 配置网格权重
        api_frame.columnconfigure(1, weight=1)
        
        # 连接测试
        test_frame = ttk.LabelFrame(scrollable_frame, text="🔍 连接测试", padding="10")
        test_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Button(test_frame, text="🧪 测试API连接", command=self.test_api_connection).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_frame, text="📋 获取可用模型", command=self.get_available_models).pack(side=tk.LEFT)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(test_frame, textvariable=self.status_var, foreground="blue")
        status_label.pack(side=tk.RIGHT)
    
    def setup_general_tab(self):
        """设置通用配置选项卡"""
        # 基本设置
        basic_frame = ttk.LabelFrame(self.general_frame, text="🔧 基本设置", padding="10")
        basic_frame.pack(fill="x", padx=10, pady=5)
        
        # OCR引擎
        ttk.Label(basic_frame, text="OCR引擎:").grid(row=0, column=0, sticky="w", pady=2)
        self.ocr_engine_var = tk.StringVar(value=self.current_config.get("ocr_engine", "paddleocr"))
        ocr_combo = ttk.Combobox(basic_frame, textvariable=self.ocr_engine_var, values=["paddleocr", "tesseract"])
        ocr_combo.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        # 截图间隔
        ttk.Label(basic_frame, text="截图间隔(秒):").grid(row=1, column=0, sticky="w", pady=2)
        self.screenshot_interval_var = tk.StringVar(value=str(self.current_config.get("screenshot_interval", "2.0")))
        ttk.Entry(basic_frame, textvariable=self.screenshot_interval_var, width=20).grid(row=1, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # 自动回复开关
        self.auto_reply_var = tk.BooleanVar(value=self.current_config.get("auto_reply", True))
        ttk.Checkbutton(basic_frame, text="启用自动回复", variable=self.auto_reply_var).grid(row=2, column=0, columnspan=2, sticky="w", pady=2)
        
        # 配置网格权重
        basic_frame.columnconfigure(1, weight=1)
        
        # 安全设置
        safety_frame = ttk.LabelFrame(self.general_frame, text="🛡️ 安全设置", padding="10")
        safety_frame.pack(fill="x", padx=10, pady=5)
        
        # 频率限制
        ttk.Label(safety_frame, text="每分钟最大回复数:").grid(row=0, column=0, sticky="w", pady=2)
        self.max_replies_var = tk.StringVar(value=str(self.current_config.get("max_replies_per_minute", "10")))
        ttk.Entry(safety_frame, textvariable=self.max_replies_var, width=20).grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # 忽略关键词
        ttk.Label(safety_frame, text="忽略关键词:").grid(row=1, column=0, sticky="w", pady=2)
        self.ignore_keywords_var = tk.StringVar(value=", ".join(self.current_config.get("ignore_keywords", [])))
        ttk.Entry(safety_frame, textvariable=self.ignore_keywords_var, width=50).grid(row=1, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        # 配置网格权重
        safety_frame.columnconfigure(1, weight=1)
    
    def setup_test_tab(self):
        """设置模型测试选项卡"""
        # 测试输入
        input_frame = ttk.LabelFrame(self.test_frame, text="💬 测试对话", padding="10")
        input_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(input_frame, text="测试消息:").pack(anchor="w")
        self.test_message_var = tk.StringVar(value="你好，请介绍一下你自己")
        test_entry = ttk.Entry(input_frame, textvariable=self.test_message_var, width=60)
        test_entry.pack(fill="x", pady=(5, 10))
        
        ttk.Button(input_frame, text="🚀 发送测试消息", command=self.test_ai_response).pack()
        
        # 测试结果
        result_frame = ttk.LabelFrame(self.test_frame, text="📝 测试结果", padding="10")
        result_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.test_result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, height=15)
        self.test_result_text.pack(fill="both", expand=True)
    
    def load_current_config(self):
        """加载当前配置"""
        config = {}
        
        # 从.env文件加载
        if os.path.exists(".env"):
            with open(".env", "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#") and "=" in line:
                        key, value = line.split("=", 1)
                        if key == "OPENAI_API_KEY":
                            config["api_key"] = value
                        elif key == "OPENAI_BASE_URL":
                            config["base_url"] = value
        
        # 从config.py加载其他配置
        try:
            from config import config as app_config
            config.update({
                "model": app_config.OPENAI_MODEL,
                "ocr_engine": app_config.OCR_ENGINE,
                "screenshot_interval": app_config.SCREENSHOT_INTERVAL,
                "auto_reply": app_config.AUTO_REPLY_ENABLED,
                "max_replies_per_minute": app_config.MAX_MESSAGES_PER_MINUTE,
                "ignore_keywords": app_config.IGNORE_KEYWORDS
            })
        except:
            pass
        
        return config
    
    def load_config_display(self):
        """加载配置到界面显示"""
        # 这个方法在setup_ui中的变量初始化时已经处理了
        pass
    
    def toggle_api_key_visibility(self):
        """切换API Key显示/隐藏"""
        if self.show_key_var.get():
            self.api_key_entry.config(show="")
        else:
            self.api_key_entry.config(show="*")
    
    def test_api_connection(self):
        """测试API连接"""
        self.status_var.set("正在测试连接...")
        self.root.update()
        
        def test_thread():
            try:
                api_key = self.api_key_var.get().strip()
                base_url = self.base_url_var.get().strip()
                
                if not api_key:
                    self.status_var.set("❌ API Key不能为空")
                    return
                
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                
                # 测试连接
                response = requests.get(f"{base_url.rstrip('/api/v1')}/api/v1/models", 
                                      headers=headers, timeout=10)
                
                if response.status_code == 200:
                    self.status_var.set("✅ 连接成功")
                else:
                    self.status_var.set(f"❌ 连接失败: {response.status_code}")
                    
            except Exception as e:
                self.status_var.set(f"❌ 连接错误: {str(e)}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def get_available_models(self):
        """获取可用模型列表"""
        self.status_var.set("正在获取模型列表...")
        self.root.update()
        
        def get_models_thread():
            try:
                api_key = self.api_key_var.get().strip()
                base_url = self.base_url_var.get().strip()
                
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                
                response = requests.get(f"{base_url.rstrip('/api/v1')}/api/v1/models", 
                                      headers=headers, timeout=15)
                
                if response.status_code == 200:
                    models = response.json()
                    model_names = [model["id"] for model in models.get("data", [])]
                    
                    # 显示模型列表
                    models_window = tk.Toplevel(self.root)
                    models_window.title("可用模型列表")
                    models_window.geometry("600x400")
                    
                    text_widget = scrolledtext.ScrolledText(models_window, wrap=tk.WORD)
                    text_widget.pack(fill="both", expand=True, padx=10, pady=10)
                    
                    text_widget.insert("1.0", f"找到 {len(model_names)} 个可用模型:\n\n")
                    for model in model_names:
                        text_widget.insert(tk.END, f"• {model}\n")
                    
                    self.status_var.set(f"✅ 找到 {len(model_names)} 个模型")
                else:
                    self.status_var.set(f"❌ 获取失败: {response.status_code}")
                    
            except Exception as e:
                self.status_var.set(f"❌ 获取错误: {str(e)}")
        
        threading.Thread(target=get_models_thread, daemon=True).start()
    
    def test_ai_response(self):
        """测试AI回复"""
        self.test_result_text.delete("1.0", tk.END)
        self.test_result_text.insert("1.0", "正在测试AI回复，请稍候...\n")
        self.root.update()
        
        def test_response_thread():
            try:
                api_key = self.api_key_var.get().strip()
                base_url = self.base_url_var.get().strip()
                model = self.model_var.get().strip()
                message = self.test_message_var.get().strip()
                
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": model,
                    "messages": [
                        {"role": "user", "content": message}
                    ],
                    "max_tokens": 200,
                    "temperature": 0.7
                }
                
                response = requests.post(f"{base_url}/chat/completions", 
                                       headers=headers, json=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    reply = result["choices"][0]["message"]["content"]
                    
                    self.test_result_text.delete("1.0", tk.END)
                    self.test_result_text.insert("1.0", f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    self.test_result_text.insert(tk.END, f"使用模型: {model}\n")
                    self.test_result_text.insert(tk.END, f"测试消息: {message}\n")
                    self.test_result_text.insert(tk.END, f"\n{'='*50}\n")
                    self.test_result_text.insert(tk.END, f"AI回复:\n{reply}\n")
                    self.test_result_text.insert(tk.END, f"{'='*50}\n")
                    self.test_result_text.insert(tk.END, f"✅ 测试成功！")
                else:
                    error_msg = f"❌ 测试失败: HTTP {response.status_code}\n{response.text}"
                    self.test_result_text.delete("1.0", tk.END)
                    self.test_result_text.insert("1.0", error_msg)
                    
            except Exception as e:
                error_msg = f"❌ 测试错误: {str(e)}"
                self.test_result_text.delete("1.0", tk.END)
                self.test_result_text.insert("1.0", error_msg)
        
        threading.Thread(target=test_response_thread, daemon=True).start()
    
    def save_config(self):
        """保存配置"""
        try:
            # 保存到.env文件
            env_content = f"""# 微信自动回复智能体环境变量配置
# OpenRouter AI配置 (已配置)
OPENAI_API_KEY={self.api_key_var.get().strip()}
OPENAI_BASE_URL={self.base_url_var.get().strip()}

# OpenAI官方API配置
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_BASE_URL=https://api.openai.com/v1

# 本地AI配置（如果使用Ollama等本地模型）
# LOCAL_AI_URL=http://localhost:11434/api/generate
# LOCAL_AI_MODEL=qwen:7b
"""
            
            with open(".env", "w", encoding="utf-8") as f:
                f.write(env_content)
            
            messagebox.showinfo("成功", "配置已保存！\n重启程序后生效。")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self.current_config = self.load_current_config()
        
        # 更新界面显示
        self.api_key_var.set(self.current_config.get("api_key", ""))
        self.base_url_var.set(self.current_config.get("base_url", "https://openrouter.ai/api/v1"))
        self.model_var.set(self.current_config.get("model", "anthropic/claude-3.5-sonnet"))
        
        messagebox.showinfo("成功", "配置已重新加载！")
    
    def open_openrouter(self):
        """打开OpenRouter网站"""
        webbrowser.open("https://openrouter.ai/")
    
    def run(self):
        """运行配置管理器"""
        self.root.mainloop()

def main():
    """主函数"""
    app = ConfigManagerGUI()
    app.run()

if __name__ == "__main__":
    main()
