@echo off
chcp 65001 >nul
echo 微信自动回复智能体安装脚本
echo ================================

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请从 https://www.python.org/downloads/ 下载并安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 安装失败，尝试使用国内镜像源...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
)

echo.
echo 创建配置文件...
if not exist .env (
    copy .env.example .env
    echo 已创建.env配置文件
)

echo.
echo 安装完成！
echo.
echo 下一步操作：
echo 1. 编辑 .env 文件，填入你的OpenAI API密钥
echo 2. 运行 start.bat 启动程序
echo.
pause
