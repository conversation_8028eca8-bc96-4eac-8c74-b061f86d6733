# 微信自动回复智能体环境变量配置示例
# 复制此文件为 .env 并填入实际配置

# OpenRouter AI配置 (推荐)
OPENAI_API_KEY=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
OPENAI_BASE_URL=https://openrouter.ai/api/v1

# OpenAI官方API配置
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_BASE_URL=https://api.openai.com/v1

# 如果使用其他兼容OpenAI API的服务，可以修改BASE_URL
# 例如使用Azure OpenAI:
# OPENAI_BASE_URL=https://your-resource.openai.azure.com/

# 本地AI配置（如果使用Ollama等本地模型）
# LOCAL_AI_URL=http://localhost:11434/api/generate
# LOCAL_AI_MODEL=qwen:7b
