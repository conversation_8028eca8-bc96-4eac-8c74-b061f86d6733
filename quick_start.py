"""
快速启动脚本 - 自动处理依赖和启动程序
"""
import sys
import subprocess
import importlib
import os

def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def install_dependencies():
    """安装必要依赖"""
    print("🔍 检查依赖包...")
    
    # 必要的依赖包
    required_packages = [
        ("pyautogui", "pyautogui"),
        ("pygetwindow", "pygetwindow"),
        ("opencv-python", "cv2"),
        ("pillow", "PIL"),
        ("python-dotenv", "dotenv"),
        ("pyyaml", "yaml"),
        ("requests", "requests"),
        ("openai", "openai"),
    ]
    
    success_count = 0
    for package_name, import_name in required_packages:
        if check_and_install_package(package_name, import_name):
            success_count += 1
    
    print(f"\n📊 依赖检查完成: {success_count}/{len(required_packages)} 成功")
    return success_count == len(required_packages)

def check_config():
    """检查配置文件"""
    print("\n🔧 检查配置文件...")
    
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ 已创建 .env 配置文件")
        else:
            print("❌ 未找到配置文件模板")
            return False
    else:
        print("✅ 配置文件存在")
    
    return True

def start_main_program():
    """启动主程序"""
    print("\n🚀 启动主程序...")
    try:
        # 导入并启动主程序
        from main import main
        main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 可以尝试:")
        print("1. 运行 python test_modules.py 进行诊断")
        print("2. 运行 python config_manager.py 检查配置")
        print("3. 查看错误日志")
        return False
    
    return True

def main():
    """主函数"""
    print("🤖 微信自动回复智能体 - 快速启动")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        input("按回车键退出...")
        return
    
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，无法启动程序")
        input("按回车键退出...")
        return
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败")
        input("按回车键退出...")
        return
    
    # 启动程序
    print("\n" + "=" * 50)
    start_main_program()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序出错: {e}")
        input("按回车键退出...")
