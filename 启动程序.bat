@echo off
chcp 65001 >nul
title 微信自动回复智能体

:start
cls
echo.
echo     🤖 微信自动回复智能体
echo     ==================
echo.
echo     选择启动方式:
echo.
echo     1. 🚀 启动主程序 (完整功能)
echo     2. 🧪 功能测试工具 (推荐)
echo     3. 🔧 配置管理器
echo     4. 📋 查看使用说明
echo     5. ❌ 退出
echo.

set /p choice=请输入选择 (1-5):

if "%choice%"=="1" goto main
if "%choice%"=="2" goto test
if "%choice%"=="3" goto config
if "%choice%"=="4" goto help
if "%choice%"=="5" goto exit
goto invalid

:main
echo.
echo 🚀 启动主程序...
python main.py
goto end

:test
echo.
echo 🧪 启动功能测试工具...
python simple_test.py
goto end

:config
echo.
echo 🔧 启动配置管理器...
python config_manager.py
goto end

:help
echo.
echo 📋 使用说明:
echo.
echo 1. 首次使用建议选择 "功能测试工具" 检查环境
echo 2. 确保微信PC版已打开并登录
echo 3. API已预配置OpenRouter服务，可直接使用
echo 4. 如需修改配置，使用 "配置管理器"
echo.
pause
goto start

:invalid
echo.
echo ❌ 无效选择，请重新输入
echo.
pause
goto start

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit

:end
echo.
echo 程序已退出
pause
goto start
