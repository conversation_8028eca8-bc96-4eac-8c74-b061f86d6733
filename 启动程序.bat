@echo off
chcp 65001 >nul
title 微信自动回复智能体

:start
cls
echo.
echo     🤖 微信自动回复智能体
echo     ==================
echo.
echo     选择启动方式:
echo.
echo     1. 🎯 精准微信回复器 (推荐新版)
echo     2. 🚀 启动主程序 (完整功能)
echo     3. 🧪 功能测试工具
echo     4. 🔧 API配置管理器
echo     5. 🪟 窗口标题管理器
echo     6. 📋 查看使用说明
echo     7. ❌ 退出
echo.

set /p choice=请输入选择 (1-7):

if "%choice%"=="1" goto precise
if "%choice%"=="2" goto main
if "%choice%"=="3" goto test
if "%choice%"=="4" goto config
if "%choice%"=="5" goto window
if "%choice%"=="6" goto help
if "%choice%"=="7" goto exit
goto invalid

:precise
echo.
echo 🎯 启动精准微信回复器...
python 精准微信回复器.py
goto end

:main
echo.
echo 🚀 启动主程序...
python main.py
goto end

:test
echo.
echo 🧪 启动功能测试工具...
python simple_test.py
goto end

:config
echo.
echo 🔧 启动API配置管理器...
python config_manager.py
goto end

:window
echo.
echo 🪟 启动窗口标题管理器...
python 窗口标题管理器.py
goto end

:help
echo.
echo 📋 使用说明:
echo.
echo 1. 推荐使用 "精准微信回复器" - 最新版本，功能完整
echo 2. 确保微信PC版已打开并登录到要监控的对话
echo 3. API已预配置OpenRouter服务，可直接使用
echo 4. 首次使用可选择 "功能测试工具" 检查环境
echo 5. 如需修改API配置，使用 "API配置管理器"
echo.
pause
goto start

:invalid
echo.
echo ❌ 无效选择，请重新输入
echo.
pause
goto start

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit

:end
echo.
echo 程序已退出
pause
goto start
