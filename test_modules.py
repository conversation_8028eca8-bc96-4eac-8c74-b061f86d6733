"""
模块功能测试脚本
"""
import sys
import time
import logging
from PIL import Image, ImageDraw, ImageFont

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_window_controller():
    """测试窗口控制模块"""
    print("=" * 50)
    print("测试窗口控制模块")
    print("=" * 50)
    
    try:
        from window_controller import WindowController
        
        controller = WindowController()
        
        # 测试查找微信窗口
        print("1. 查找微信窗口...")
        window = controller.find_wechat_window()
        if window:
            print(f"✓ 找到微信窗口: {window.title}")
            print(f"  位置: ({window.left}, {window.top})")
            print(f"  大小: {window.width} x {window.height}")
        else:
            print("✗ 未找到微信窗口")
        
        # 测试获取窗口区域
        print("\n2. 获取窗口区域...")
        region = controller.get_window_region()
        if region:
            print(f"✓ 窗口区域: {region}")
        else:
            print("✗ 获取窗口区域失败")
        
        # 测试截图
        print("\n3. 测试截图功能...")
        screenshot = controller.capture_window()
        if screenshot:
            print(f"✓ 截图成功，尺寸: {screenshot.size}")
            screenshot.save("test_window_screenshot.png")
            print("  截图已保存为 test_window_screenshot.png")
        else:
            print("✗ 截图失败")
        
        # 测试聊天区域截图
        print("\n4. 测试聊天区域截图...")
        chat_screenshot = controller.capture_chat_area()
        if chat_screenshot:
            print(f"✓ 聊天区域截图成功，尺寸: {chat_screenshot.size}")
            chat_screenshot.save("test_chat_screenshot.png")
            print("  截图已保存为 test_chat_screenshot.png")
        else:
            print("✗ 聊天区域截图失败")
        
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_ocr_processor():
    """测试OCR处理模块"""
    print("\n" + "=" * 50)
    print("测试OCR处理模块")
    print("=" * 50)
    
    try:
        from ocr_processor import OCRProcessor
        
        # 创建测试图片
        print("1. 创建测试图片...")
        test_image = create_test_image()
        test_image.save("test_ocr_image.png")
        print("✓ 测试图片已创建: test_ocr_image.png")
        
        # 测试OCR识别
        print("\n2. 测试OCR识别...")
        processor = OCRProcessor()
        
        texts = processor.extract_text(test_image)
        if texts:
            print(f"✓ OCR识别成功，识别到 {len(texts)} 行文字:")
            for i, text in enumerate(texts, 1):
                print(f"  {i}. {text}")
        else:
            print("✗ OCR识别失败或未识别到文字")
        
        # 测试消息过滤
        print("\n3. 测试消息过滤...")
        filtered_messages = processor.filter_messages(texts)
        if filtered_messages:
            print(f"✓ 过滤后得到 {len(filtered_messages)} 条有效消息:")
            for i, msg in enumerate(filtered_messages, 1):
                print(f"  {i}. {msg}")
        else:
            print("✗ 未找到有效消息")
        
        # 测试获取最新消息
        print("\n4. 测试获取最新消息...")
        latest_message = processor.get_latest_message(texts)
        if latest_message:
            print(f"✓ 最新消息: {latest_message}")
        else:
            print("✗ 未找到最新消息")
        
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_ai_responder():
    """测试AI回复模块"""
    print("\n" + "=" * 50)
    print("测试AI回复模块")
    print("=" * 50)
    
    try:
        from ai_responder import AIResponder
        
        responder = AIResponder()
        
        # 测试回复判断
        print("1. 测试回复判断...")
        test_messages = [
            "你好",
            "今天天气怎么样？",
            "系统消息",
            "[图片]",
            "谢谢"
        ]
        
        for msg in test_messages:
            should_reply = responder.should_reply(msg)
            print(f"  '{msg}' -> {'应该回复' if should_reply else '不回复'}")
        
        # 测试备用回复
        print("\n2. 测试备用回复...")
        fallback_reply = responder.get_fallback_reply("测试消息")
        print(f"✓ 备用回复: {fallback_reply}")
        
        # 测试AI回复生成（如果配置了API）
        print("\n3. 测试AI回复生成...")
        try:
            reply = responder.generate_reply("你好，请问你是谁？", "测试用户")
            if reply:
                print(f"✓ AI回复: {reply}")
            else:
                print("✗ AI回复生成失败（可能是API未配置）")
        except Exception as e:
            print(f"✗ AI回复生成出错: {e}")
        
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_config():
    """测试配置模块"""
    print("\n" + "=" * 50)
    print("测试配置模块")
    print("=" * 50)
    
    try:
        from config import config
        
        print("1. 配置信息:")
        print(f"  OCR引擎: {config.OCR_ENGINE}")
        print(f"  AI提供商: {config.AI_PROVIDER}")
        print(f"  截图间隔: {config.SCREENSHOT_INTERVAL}秒")
        print(f"  自动回复: {'启用' if config.AUTO_REPLY_ENABLED else '禁用'}")
        print(f"  最大回复长度: {config.MAX_REPLY_LENGTH}")
        print(f"  忽略关键词: {config.IGNORE_KEYWORDS}")
        
        print("\n✓ 配置加载成功")
        
    except ImportError as e:
        print(f"✗ 导入配置失败: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def create_test_image():
    """创建测试图片"""
    # 创建一个白色背景的图片
    width, height = 400, 200
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体
    try:
        # Windows系统字体
        font = ImageFont.truetype("msyh.ttc", 16)  # 微软雅黑
    except:
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
    
    # 绘制测试文字
    test_texts = [
        "你好，这是一条测试消息",
        "今天天气很好",
        "请问有什么可以帮助你的吗？",
        "谢谢你的回复"
    ]
    
    y_offset = 20
    for text in test_texts:
        draw.text((20, y_offset), text, fill='black', font=font)
        y_offset += 30
    
    return image

def main():
    """主测试函数"""
    print("微信自动回复智能体 - 模块测试")
    print("=" * 60)
    
    # 测试各个模块
    test_config()
    test_window_controller()
    test_ocr_processor()
    test_ai_responder()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n注意事项:")
    print("1. 如果窗口控制测试失败，请确保微信已打开")
    print("2. 如果OCR测试失败，请检查PaddleOCR是否正确安装")
    print("3. 如果AI回复测试失败，请检查API配置")
    print("4. 测试图片已保存到当前目录")

if __name__ == "__main__":
    main()
