# 🚀 微信自动回复智能体 - 快速启动指南

## ✅ 配置状态

**🎉 恭喜！您的OpenRouter AI已经配置完成，可以直接使用！**

- ✅ API Key: 已配置 (sk-or-v1-885379cc...)
- ✅ Base URL: https://openrouter.ai/api/v1
- ✅ 默认模型: anthropic/claude-3.5-sonnet
- ✅ 连接测试: 通过 (323个可用模型)

## 🎯 立即开始使用

### 方法一：使用启动脚本（推荐）
```bash
# 双击运行
start.bat
```

### 方法二：命令行启动
```bash
# 启动主程序
python main.py

# 或启动配置管理器
python config_manager.py
```

### 方法三：测试功能
```bash
# 运行功能演示
python demo.py

# 测试API连接
python test_openrouter.py

# 测试各模块
python test_modules.py
```

## 📋 使用步骤

### 1. 准备微信环境
- 确保微信PC版已安装并登录
- 打开微信窗口（不要最小化）
- 进入任意聊天对话

### 2. 启动程序
- 运行 `start.bat` 或 `python main.py`
- 程序会自动打开GUI控制界面

### 3. 开始监控
- 点击"开始监控"按钮
- 查看状态信息确认微信窗口已连接
- 观察消息历史区域的日志输出

### 4. 测试功能
- 点击"截图预览"查看当前微信窗口截图
- 使用"手动回复"功能测试消息发送
- 让朋友发送测试消息验证自动回复

## 🔧 配置管理

### 打开配置管理器
- 在主程序中点击"🔧 API配置"按钮
- 或运行 `python config_manager.py`
- 或双击 `config.bat`

### 配置管理器功能
- 🌐 **OpenRouter配置**: 管理API密钥和模型选择
- ⚙️ **通用配置**: 调整OCR引擎、截图间隔等
- 🧪 **模型测试**: 测试API连接和AI回复功能

## 📊 当前配置详情

### AI服务配置
```
提供商: OpenRouter AI
API Key: 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
Base URL: https://openrouter.ai/api/v1
默认模型: anthropic/claude-3.5-sonnet
```

### 功能配置
```
OCR引擎: PaddleOCR
截图间隔: 2.0秒
自动回复: 启用
频率限制: 10条/分钟
```

## 🎛️ 界面说明

### 主程序界面
- **控制面板**: 开始/停止监控、截图预览、设置
- **运行信息**: 显示OCR引擎、AI提供商、微信窗口状态
- **消息历史**: 实时显示收到的消息和发送的回复
- **手动回复**: 可以手动发送消息到微信

### 配置管理器界面
- **OpenRouter AI**: 管理API配置和模型选择
- **通用配置**: 调整基本功能参数
- **模型测试**: 测试AI回复功能

## 🛡️ 安全提醒

### 使用建议
- ✅ 仅在个人微信账号上使用
- ✅ 避免在重要群聊中启用自动回复
- ✅ 定期检查回复内容是否合适
- ✅ 遵守微信使用条款

### 频率控制
- 默认限制：每分钟最多10条回复
- 可在配置中调整频率限制
- 支持关键词过滤功能

## 🔍 故障排除

### 常见问题

**1. 找不到微信窗口**
- 确保微信已打开且未最小化
- 检查窗口标题是否为"微信"
- 尝试重新启动微信

**2. OCR识别率低**
- 调整微信窗口大小和位置
- 确保聊天区域清晰可见
- 尝试切换OCR引擎

**3. AI回复失败**
- 检查网络连接
- 运行 `python test_openrouter.py` 测试API
- 查看程序日志了解错误详情

**4. 发送消息失败**
- 确保微信窗口处于前台
- 检查输入框位置是否正确
- 尝试手动点击微信输入框

### 日志查看
- 程序日志: `wechat_ai.log`
- GUI界面: 消息历史区域
- 详细错误: 运行测试脚本查看

## 📚 更多资源

### 文档
- `README.md`: 详细使用说明
- `项目结构说明.md`: 项目架构介绍
- `openrouter_info.html`: OpenRouter配置指南

### 工具脚本
- `demo.py`: 功能演示
- `test_modules.py`: 模块测试
- `test_openrouter.py`: API测试
- `setup_wizard.py`: 设置向导

### 配置文件
- `.env`: 环境变量配置
- `config.py`: 主要功能配置
- `requirements.txt`: 依赖包列表

## 🎉 开始体验

现在您已经完成了所有配置，可以开始体验微信自动回复功能了！

1. **启动程序**: 双击 `start.bat` 或运行 `python main.py`
2. **开始监控**: 点击"开始监控"按钮
3. **测试功能**: 让朋友发送消息测试自动回复
4. **享受智能**: 体验AI驱动的智能对话

**祝您使用愉快！** 🎊

---

*如有问题，请查看详细文档或运行测试脚本进行诊断。*
