"""
微信自动回复智能体演示脚本
用于展示项目的主要功能
"""
import time
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_window_detection():
    """演示窗口检测功能"""
    print("\n" + "="*60)
    print("🔍 演示功能：微信窗口检测")
    print("="*60)
    
    try:
        from window_controller import WindowController
        
        controller = WindowController()
        print("正在查找微信窗口...")
        
        window = controller.find_wechat_window()
        if window:
            print(f"✅ 成功找到微信窗口！")
            print(f"   窗口标题: {window.title}")
            print(f"   窗口位置: ({window.left}, {window.top})")
            print(f"   窗口大小: {window.width} x {window.height}")
            print(f"   窗口状态: {'最小化' if window.isMinimized else '正常'}")
            
            # 获取窗口区域
            region = controller.get_window_region()
            if region:
                print(f"   监控区域: {region}")
            
            return True
        else:
            print("❌ 未找到微信窗口")
            print("   请确保：")
            print("   1. 微信PC版已经打开")
            print("   2. 微信窗口没有被最小化")
            print("   3. 微信窗口标题为'微信'")
            return False
            
    except Exception as e:
        print(f"❌ 窗口检测失败: {e}")
        return False

def demo_screenshot():
    """演示截图功能"""
    print("\n" + "="*60)
    print("📸 演示功能：窗口截图")
    print("="*60)
    
    try:
        from window_controller import WindowController
        
        controller = WindowController()
        
        # 截取完整窗口
        print("正在截取微信窗口...")
        screenshot = controller.capture_window()
        
        if screenshot:
            print(f"✅ 窗口截图成功！")
            print(f"   图片尺寸: {screenshot.size}")
            screenshot.save("demo_window.png")
            print("   截图已保存为: demo_window.png")
            
            # 截取聊天区域
            print("\n正在截取聊天区域...")
            chat_screenshot = controller.capture_chat_area()
            
            if chat_screenshot:
                print(f"✅ 聊天区域截图成功！")
                print(f"   图片尺寸: {chat_screenshot.size}")
                chat_screenshot.save("demo_chat.png")
                print("   截图已保存为: demo_chat.png")
                return True
            else:
                print("❌ 聊天区域截图失败")
                return False
        else:
            print("❌ 窗口截图失败")
            return False
            
    except Exception as e:
        print(f"❌ 截图功能失败: {e}")
        return False

def demo_ocr():
    """演示OCR识别功能"""
    print("\n" + "="*60)
    print("🔤 演示功能：OCR文字识别")
    print("="*60)
    
    try:
        from ocr_processor import OCRProcessor
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建测试图片
        print("正在创建测试图片...")
        test_image = create_demo_image()
        test_image.save("demo_ocr_test.png")
        print("✅ 测试图片已创建: demo_ocr_test.png")
        
        # 初始化OCR处理器
        print("\n正在初始化OCR引擎...")
        processor = OCRProcessor()
        print(f"✅ OCR引擎初始化成功: {processor.engine}")
        
        # 进行OCR识别
        print("\n正在进行文字识别...")
        texts = processor.extract_text(test_image)
        
        if texts:
            print(f"✅ OCR识别成功！识别到 {len(texts)} 行文字:")
            for i, text in enumerate(texts, 1):
                print(f"   {i}. {text}")
            
            # 测试消息过滤
            print("\n正在过滤有效消息...")
            filtered = processor.filter_messages(texts)
            if filtered:
                print(f"✅ 过滤后得到 {len(filtered)} 条有效消息:")
                for i, msg in enumerate(filtered, 1):
                    print(f"   {i}. {msg}")
                
                # 获取最新消息
                latest = processor.get_latest_message(texts)
                if latest:
                    print(f"\n📝 最新消息: {latest}")
                    return True
            else:
                print("❌ 未找到有效消息")
                return False
        else:
            print("❌ OCR识别失败，未识别到任何文字")
            return False
            
    except Exception as e:
        print(f"❌ OCR功能失败: {e}")
        return False

def demo_ai_response():
    """演示AI回复功能"""
    print("\n" + "="*60)
    print("🤖 演示功能：AI智能回复")
    print("="*60)
    
    try:
        from ai_responder import AIResponder
        
        # 初始化AI回复器
        print("正在初始化AI回复器...")
        responder = AIResponder()
        print(f"✅ AI回复器初始化成功: {responder.provider}")
        
        # 测试消息
        test_messages = [
            "你好",
            "今天天气怎么样？",
            "请问你能帮我做什么？",
            "谢谢你的帮助",
            "系统消息",  # 应该被过滤
            "[图片]",    # 应该被过滤
        ]
        
        print("\n正在测试消息回复判断...")
        for msg in test_messages:
            should_reply = responder.should_reply(msg)
            status = "✅ 应该回复" if should_reply else "❌ 不回复"
            print(f"   '{msg}' -> {status}")
        
        # 测试备用回复
        print("\n正在测试备用回复...")
        fallback = responder.get_fallback_reply("测试消息")
        print(f"✅ 备用回复: {fallback}")
        
        # 测试AI回复生成
        print("\n正在测试AI回复生成...")
        test_msg = "你好，请问你是谁？"
        print(f"用户消息: {test_msg}")
        
        try:
            reply = responder.generate_reply(test_msg, "演示用户")
            if reply:
                print(f"✅ AI回复: {reply}")
                return True
            else:
                print("❌ AI回复生成失败（可能是API未配置）")
                print("💡 提示: 请在.env文件中配置OPENAI_API_KEY")
                return False
        except Exception as e:
            print(f"❌ AI回复生成出错: {e}")
            print("💡 提示: 请检查API配置和网络连接")
            return False
            
    except Exception as e:
        print(f"❌ AI回复功能失败: {e}")
        return False

def demo_full_workflow():
    """演示完整工作流程"""
    print("\n" + "="*60)
    print("🔄 演示功能：完整工作流程")
    print("="*60)
    
    try:
        from wechat_monitor import WeChatMonitor
        
        # 创建监控器
        print("正在初始化微信监控器...")
        monitor = WeChatMonitor()
        
        # 设置回调函数
        def on_message(msg):
            print(f"📨 收到消息: {msg}")
        
        def on_reply(original, reply):
            print(f"🤖 自动回复: {reply}")
        
        def on_error(error):
            print(f"❌ 错误: {error}")
        
        monitor.set_callbacks(on_message, on_reply, on_error)
        
        # 获取状态
        status = monitor.get_status()
        print("✅ 监控器初始化成功")
        print(f"   监控状态: {'运行中' if status['is_monitoring'] else '未启动'}")
        print(f"   微信窗口: {'已连接' if status['wechat_window_found'] else '未检测'}")
        print(f"   OCR引擎: {status['ocr_engine']}")
        print(f"   AI提供商: {status['ai_provider']}")
        
        # 测试截图
        print("\n正在测试当前截图...")
        screenshot = monitor.get_current_screenshot()
        if screenshot:
            print(f"✅ 获取截图成功: {screenshot.size}")
            screenshot.save("demo_current.png")
            print("   截图已保存为: demo_current.png")
        else:
            print("❌ 获取截图失败")
        
        print("\n💡 完整工作流程演示完成")
        print("   实际使用时，监控器会:")
        print("   1. 持续监控微信窗口变化")
        print("   2. 自动识别新消息内容")
        print("   3. 生成智能回复并发送")
        print("   4. 记录所有操作日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程演示失败: {e}")
        return False

def create_demo_image():
    """创建演示用的测试图片"""
    from PIL import Image, ImageDraw, ImageFont
    
    # 创建图片
    width, height = 500, 300
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体
    try:
        font_large = ImageFont.truetype("msyh.ttc", 20)
        font_small = ImageFont.truetype("msyh.ttc", 14)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 绘制标题
    draw.text((20, 20), "微信聊天演示", fill='black', font=font_large)
    
    # 绘制消息
    messages = [
        "小明: 你好，在吗？",
        "小红: 在的，有什么事吗？",
        "小明: 今天天气真不错",
        "小红: 是的，适合出去走走",
        "小明: 要不要一起去公园？",
        "小红: 好啊，几点见面？"
    ]
    
    y_offset = 60
    for msg in messages:
        draw.text((30, y_offset), msg, fill='black', font=font_small)
        y_offset += 25
    
    # 绘制边框
    draw.rectangle([(10, 10), (width-10, height-10)], outline='gray', width=2)
    
    return image

def main():
    """主演示函数"""
    print("🎉 微信自动回复智能体 - 功能演示")
    print("=" * 80)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 演示各个功能
    results = []
    
    print("\n🚀 开始功能演示...")
    
    # 1. 窗口检测
    results.append(("窗口检测", demo_window_detection()))
    
    # 2. 截图功能
    results.append(("截图功能", demo_screenshot()))
    
    # 3. OCR识别
    results.append(("OCR识别", demo_ocr()))
    
    # 4. AI回复
    results.append(("AI回复", demo_ai_response()))
    
    # 5. 完整流程
    results.append(("完整流程", demo_full_workflow()))
    
    # 显示演示结果
    print("\n" + "="*80)
    print("📊 演示结果汇总")
    print("="*80)
    
    success_count = 0
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{name:12} : {status}")
        if success:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 所有功能演示成功！项目已准备就绪。")
    elif success_count >= len(results) * 0.7:
        print("\n⚠️  大部分功能正常，请检查失败的功能模块。")
    else:
        print("\n❌ 多个功能存在问题，请检查环境配置和依赖安装。")
    
    print("\n💡 使用提示:")
    print("1. 运行 python main.py 启动完整程序")
    print("2. 运行 python test_modules.py 进行详细测试")
    print("3. 查看 README.md 了解详细使用说明")
    print("4. 检查生成的演示图片文件")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程出错: {e}")
        logger.exception("演示失败")
    finally:
        print("\n感谢使用微信自动回复智能体！")
