"""
工具函数模块
"""
import os
import json
import time
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def ensure_dir(directory: str):
    """确保目录存在"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def save_json(data: Dict[Any, Any], filepath: str):
    """保存JSON文件"""
    try:
        ensure_dir(os.path.dirname(filepath))
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存JSON文件失败: {e}")
        return False

def load_json(filepath: str) -> Optional[Dict[Any, Any]]:
    """加载JSON文件"""
    try:
        if not os.path.exists(filepath):
            return None
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件失败: {e}")
        return None

def get_file_hash(filepath: str) -> Optional[str]:
    """获取文件MD5哈希值"""
    try:
        if not os.path.exists(filepath):
            return None
        
        hash_md5 = hashlib.md5()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"计算文件哈希失败: {e}")
        return None

def format_timestamp(timestamp: float = None) -> str:
    """格式化时间戳"""
    if timestamp is None:
        timestamp = time.time()
    return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

def truncate_text(text: str, max_length: int = 100) -> str:
    """截断文本"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def is_chinese_char(char: str) -> bool:
    """判断是否为中文字符"""
    return '\u4e00' <= char <= '\u9fff'

def count_chinese_chars(text: str) -> int:
    """统计中文字符数量"""
    return sum(1 for char in text if is_chinese_char(char))

def clean_filename(filename: str) -> str:
    """清理文件名中的非法字符"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def retry_on_exception(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"函数 {func.__name__} 第{attempt+1}次调用失败: {e}, 重试中...")
                        time.sleep(delay)
                    else:
                        logger.error(f"函数 {func.__name__} 重试{max_retries}次后仍然失败")
            raise last_exception
        return wrapper
    return decorator

class RateLimiter:
    """速率限制器"""
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def can_call(self) -> bool:
        """检查是否可以调用"""
        now = time.time()
        # 清理过期的调用记录
        self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
        
        return len(self.calls) < self.max_calls
    
    def record_call(self):
        """记录一次调用"""
        self.calls.append(time.time())
    
    def wait_time(self) -> float:
        """获取需要等待的时间"""
        if self.can_call():
            return 0.0
        
        if not self.calls:
            return 0.0
        
        oldest_call = min(self.calls)
        return self.time_window - (time.time() - oldest_call)

class ConfigManager:
    """配置管理器"""
    def __init__(self, config_file: str = "user_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        config = load_json(self.config_file)
        if config is None:
            config = {}
        return config
    
    def save_config(self) -> bool:
        """保存配置"""
        return save_json(self.config, self.config_file)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置项"""
        self.config[key] = value
    
    def update(self, updates: Dict[str, Any]):
        """批量更新配置"""
        self.config.update(updates)

class MessageCache:
    """消息缓存"""
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = []
    
    def add_message(self, message: str, timestamp: float = None):
        """添加消息到缓存"""
        if timestamp is None:
            timestamp = time.time()
        
        self.cache.append({
            "message": message,
            "timestamp": timestamp,
            "hash": hashlib.md5(message.encode()).hexdigest()
        })
        
        # 限制缓存大小
        if len(self.cache) > self.max_size:
            self.cache = self.cache[-self.max_size:]
    
    def is_duplicate(self, message: str, time_window: float = 60.0) -> bool:
        """检查是否为重复消息"""
        message_hash = hashlib.md5(message.encode()).hexdigest()
        now = time.time()
        
        for cached in reversed(self.cache):  # 从最新的开始检查
            if now - cached["timestamp"] > time_window:
                break
            if cached["hash"] == message_hash:
                return True
        
        return False
    
    def get_recent_messages(self, count: int = 10) -> list:
        """获取最近的消息"""
        return self.cache[-count:] if len(self.cache) >= count else self.cache
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
