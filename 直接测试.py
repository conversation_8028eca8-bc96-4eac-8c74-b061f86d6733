"""
直接测试脚本 - 在命令行中显示测试按钮选项
"""
import sys
import os

def show_menu():
    """显示测试菜单"""
    print("\n" + "="*60)
    print("🤖 微信自动回复智能体 - 测试菜单")
    print("="*60)
    print()
    print("请选择要执行的测试:")
    print()
    print("1. 🧪 测试模块导入")
    print("2. 🔧 测试配置文件")
    print("3. 🔍 测试微信窗口检测")
    print("4. 🌐 测试API连接")
    print("5. 🚀 启动主程序")
    print("6. 🔧 启动配置管理器")
    print("7. 📋 查看所有测试结果")
    print("8. ❌ 退出")
    print()

def test_imports():
    """测试导入"""
    print("\n🧪 测试模块导入...")
    print("-" * 40)
    
    modules = [
        ("pyautogui", "pyautogui"),
        ("pygetwindow", "pygetwindow"),
        ("opencv-python", "cv2"),
        ("pillow", "PIL"),
        ("openai", "openai"),
        ("python-dotenv", "dotenv"),
        ("pyyaml", "yaml"),
        ("requests", "requests")
    ]
    
    success_count = 0
    for package_name, import_name in modules:
        try:
            __import__(import_name)
            print(f"✅ {package_name} - 导入成功")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} - 导入失败")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)

def test_config():
    """测试配置"""
    print("\n🔧 测试配置文件...")
    print("-" * 40)
    
    if not os.path.exists(".env"):
        print("❌ .env 文件不存在")
        return False
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        
        if api_key:
            print(f"✅ API Key: {api_key[:20]}...{api_key[-10:]}")
        else:
            print("❌ API Key 未配置")
            return False
        
        if base_url:
            print(f"✅ Base URL: {base_url}")
        else:
            print("❌ Base URL 未配置")
            return False
        
        print("✅ 配置文件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_window():
    """测试窗口检测"""
    print("\n🔍 测试微信窗口检测...")
    print("-" * 40)
    
    try:
        import pygetwindow as gw
        
        # 获取所有窗口
        windows = gw.getAllWindows()
        print(f"✅ 系统中找到 {len(windows)} 个窗口")
        
        # 查找微信窗口
        wechat_windows = gw.getWindowsWithTitle("微信")
        if wechat_windows:
            print(f"✅ 找到 {len(wechat_windows)} 个微信窗口:")
            for i, window in enumerate(wechat_windows):
                print(f"   {i+1}. {window.title}")
                print(f"      位置: ({window.left}, {window.top})")
                print(f"      大小: {window.width} x {window.height}")
                print(f"      状态: {'最小化' if window.isMinimized else '正常'}")
            return True
        else:
            print("⚠️ 未找到微信窗口")
            print("💡 请确保:")
            print("   1. 微信PC版已打开")
            print("   2. 微信窗口未最小化")
            print("   3. 微信窗口标题为'微信'")
            return False
            
    except Exception as e:
        print(f"❌ 窗口检测失败: {e}")
        return False

def test_api():
    """测试API连接"""
    print("\n🌐 测试API连接...")
    print("-" * 40)
    
    try:
        import requests
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
        
        print(f"🔗 连接地址: {base_url}")
        print("⏳ 正在测试连接...")
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试获取模型列表
        response = requests.get(f"{base_url}/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            model_count = len(models.get("data", []))
            print(f"✅ API连接成功")
            print(f"📊 可用模型数量: {model_count}")
            
            # 显示热门模型
            popular_models = [
                "anthropic/claude-3.5-sonnet",
                "openai/gpt-4-turbo", 
                "openai/gpt-3.5-turbo"
            ]
            
            print("🔥 热门模型状态:")
            for model in models.get("data", []):
                model_id = model.get("id", "")
                if model_id in popular_models:
                    print(f"   ✅ {model_id}")
            
            return True
        else:
            print(f"❌ API连接失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def start_main():
    """启动主程序"""
    print("\n🚀 启动主程序...")
    print("-" * 40)
    
    try:
        import subprocess
        subprocess.Popen([sys.executable, "main.py"])
        print("✅ 主程序已启动")
        print("💡 请查看新打开的窗口")
        return True
    except Exception as e:
        print(f"❌ 启动主程序失败: {e}")
        return False

def start_config():
    """启动配置管理器"""
    print("\n🔧 启动配置管理器...")
    print("-" * 40)
    
    try:
        import subprocess
        subprocess.Popen([sys.executable, "config_manager.py"])
        print("✅ 配置管理器已启动")
        print("💡 请查看新打开的窗口")
        return True
    except Exception as e:
        print(f"❌ 启动配置管理器失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("\n📋 运行所有测试...")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config),
        ("窗口检测", test_window),
        ("API连接", test_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    # 显示汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过！程序可以正常使用。")
    elif success_count >= 3:
        print("⚠️ 大部分测试通过，基本功能可用。")
    else:
        print("❌ 多项测试失败，请检查环境配置。")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-8): ").strip()
            
            if choice == "1":
                test_imports()
            elif choice == "2":
                test_config()
            elif choice == "3":
                test_window()
            elif choice == "4":
                test_api()
            elif choice == "5":
                start_main()
            elif choice == "6":
                start_config()
            elif choice == "7":
                run_all_tests()
            elif choice == "8":
                print("\n👋 再见！")
                break
            else:
                print("\n❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
