<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRouter AI 配置指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #4facfe;
        }
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .section h2::before {
            content: "🔹";
            margin-right: 10px;
            font-size: 1.2em;
        }
        .config-box {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            position: relative;
            overflow-x: auto;
        }
        .config-box::before {
            content: "配置信息";
            position: absolute;
            top: -10px;
            left: 15px;
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .model-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-color: #4facfe;
        }
        .model-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .model-card .provider {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px 5px;
            transition: transform 0.3s ease;
            font-weight: bold;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 OpenRouter AI 配置指南</h1>
            <p>微信自动回复智能体 - AI服务配置</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>当前配置状态</h2>
                <div class="highlight">
                    <p><span class="status-indicator"></span><strong>配置状态：已完成</strong></p>
                    <p>✅ API Key 已配置</p>
                    <p>✅ Base URL 已设置</p>
                    <p>✅ 默认模型已选择</p>
                </div>
                
                <div class="config-box">
API Key: 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
Base URL: https://openrouter.ai/api/v1
默认模型: anthropic/claude-3.5-sonnet
                </div>
            </div>
            
            <div class="section">
                <h2>OpenRouter AI 简介</h2>
                <p>OpenRouter 是一个AI模型聚合平台，提供统一的API接口访问多种先进的AI模型。它的主要优势包括：</p>
                <ul>
                    <li>🌟 <strong>多模型支持</strong>：集成了OpenAI、Anthropic、Google、Meta等多家公司的模型</li>
                    <li>🔌 <strong>统一接口</strong>：兼容OpenAI API格式，易于集成</li>
                    <li>💰 <strong>灵活计费</strong>：按使用量付费，支持多种付费方式</li>
                    <li>⚡ <strong>高可用性</strong>：稳定的服务质量和快速响应</li>
                    <li>🛡️ <strong>安全可靠</strong>：企业级安全保障</li>
                </ul>
            </div>
            
            <div class="section">
                <h2>支持的热门模型</h2>
                <div class="models-grid">
                    <div class="model-card">
                        <h3>Claude 3.5 Sonnet</h3>
                        <p class="provider">Anthropic</p>
                        <p>最新最强的对话模型，推荐使用</p>
                    </div>
                    <div class="model-card">
                        <h3>GPT-4 Turbo</h3>
                        <p class="provider">OpenAI</p>
                        <p>OpenAI最新的大型语言模型</p>
                    </div>
                    <div class="model-card">
                        <h3>GPT-3.5 Turbo</h3>
                        <p class="provider">OpenAI</p>
                        <p>性价比高的经典模型</p>
                    </div>
                    <div class="model-card">
                        <h3>Llama 3</h3>
                        <p class="provider">Meta</p>
                        <p>开源大型语言模型</p>
                    </div>
                    <div class="model-card">
                        <h3>Gemini Pro</h3>
                        <p class="provider">Google</p>
                        <p>Google的多模态AI模型</p>
                    </div>
                    <div class="model-card">
                        <h3>Mixtral 8x7B</h3>
                        <p class="provider">Mistral AI</p>
                        <p>高性能混合专家模型</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>使用说明</h2>
                <ol>
                    <li><strong>API配置</strong>：您的API密钥已经配置完成，可以直接使用</li>
                    <li><strong>模型选择</strong>：默认使用Claude 3.5 Sonnet，您可以在配置管理器中更改</li>
                    <li><strong>测试连接</strong>：使用配置管理器测试API连接是否正常</li>
                    <li><strong>开始使用</strong>：启动主程序开始使用微信自动回复功能</li>
                </ol>
                
                <div class="highlight">
                    <p><strong>💡 提示：</strong>如需修改配置，请使用项目中的配置管理器工具，或直接编辑 .env 文件。</p>
                </div>
            </div>
            
            <div class="section">
                <h2>快速操作</h2>
                <div style="text-align: center;">
                    <a href="#" class="btn" onclick="openConfigManager()">🔧 打开配置管理器</a>
                    <a href="#" class="btn" onclick="openMainApp()">🚀 启动主程序</a>
                    <a href="https://openrouter.ai/" class="btn" target="_blank">🌐 访问OpenRouter官网</a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>微信自动回复智能体 v1.0.0 | 基于OpenRouter AI技术</p>
            <p>配置完成时间：<span id="currentTime"></span></p>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');
        
        function openConfigManager() {
            alert('请运行 config_manager.py 或点击主程序中的"API配置"按钮');
        }
        
        function openMainApp() {
            alert('请运行 main.py 或双击 start.bat 启动主程序');
        }
        
        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.model-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
