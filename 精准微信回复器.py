"""
精准微信自动回复器 - 监控特定窗口标题并自动回复
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pygetwindow as gw
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageGrab
import time
import threading
import requests
import json
import os
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class PreciseWeChatReplier:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("精准微信自动回复器")
        self.root.geometry("1000x700")
        
        # 核心变量
        self.target_window = None
        self.target_title = ""
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_screenshot = None
        
        # API配置
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎯 精准微信自动回复器", font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 第一步：选择目标窗口
        step1_frame = ttk.LabelFrame(main_frame, text="第一步：选择要监控的微信窗口", padding="10")
        step1_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step1_frame.columnconfigure(1, weight=1)
        
        # 刷新按钮
        ttk.Button(step1_frame, text="🔄 刷新窗口", command=self.refresh_windows).grid(row=0, column=0, padx=(0, 10))
        
        # 窗口选择下拉框
        self.window_var = tk.StringVar()
        self.window_combo = ttk.Combobox(step1_frame, textvariable=self.window_var, width=50, state="readonly")
        self.window_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.window_combo.bind('<<ComboboxSelected>>', self.on_window_selected)
        
        # 选择按钮
        ttk.Button(step1_frame, text="✅ 选择此窗口", command=self.select_target_window).grid(row=0, column=2)
        
        # 当前选择显示
        self.selected_label = ttk.Label(step1_frame, text="未选择窗口", foreground="red")
        self.selected_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 第二步：监控控制
        step2_frame = ttk.LabelFrame(main_frame, text="第二步：监控控制", padding="10")
        step2_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        step2_frame.columnconfigure(0, weight=1)
        step2_frame.rowconfigure(1, weight=1)
        
        # 控制按钮
        control_frame = ttk.Frame(step2_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="🚀 开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ 停止监控", command=self.stop_monitoring, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📸 测试截图", command=self.test_screenshot).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🧪 测试发送", command=self.test_send_message).pack(side=tk.LEFT)
        
        # 监控日志
        ttk.Label(step2_frame, text="监控日志:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(0, 5))
        
        self.log_text = scrolledtext.ScrolledText(step2_frame, height=15, font=("Consolas", 9))
        self.log_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 第三步：手动测试
        step3_frame = ttk.LabelFrame(main_frame, text="第三步：手动测试回复", padding="10")
        step3_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        step3_frame.columnconfigure(0, weight=1)
        step3_frame.rowconfigure(1, weight=1)
        
        # 测试消息输入
        ttk.Label(step3_frame, text="测试消息:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.test_message_var = tk.StringVar(value="你好，请问你是谁？")
        test_entry = ttk.Entry(step3_frame, textvariable=self.test_message_var)
        test_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # AI回复测试
        ttk.Button(step3_frame, text="🤖 获取AI回复", command=self.test_ai_reply).grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 回复显示
        ttk.Label(step3_frame, text="AI回复:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        self.reply_text = scrolledtext.ScrolledText(step3_frame, height=8, font=("微软雅黑", 9))
        self.reply_text.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 发送回复
        ttk.Button(step3_frame, text="📤 发送回复", command=self.send_test_reply).grid(row=5, column=0, sticky=(tk.W, tk.E))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        self.log("🎯 精准微信自动回复器已启动")
        self.log("请按步骤操作：1.选择窗口 → 2.开始监控 → 3.测试回复")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
    
    def refresh_windows(self):
        """刷新微信窗口列表"""
        try:
            self.log("🔍 正在刷新微信窗口列表...")
            
            # 获取所有窗口
            all_windows = gw.getAllWindows()
            wechat_windows = []
            
            for window in all_windows:
                title = window.title.strip()
                if ("微信" in title and 
                    len(title) > 0 and 
                    window.visible and 
                    window.width > 300 and 
                    window.height > 200):
                    wechat_windows.append(f"{title} ({window.width}x{window.height})")
            
            # 更新下拉框
            self.window_combo['values'] = wechat_windows
            
            if wechat_windows:
                self.log(f"✅ 找到 {len(wechat_windows)} 个微信窗口")
                self.window_combo.current(0)  # 默认选择第一个
            else:
                self.log("❌ 未找到微信窗口，请确保微信已打开")
                
        except Exception as e:
            self.log(f"❌ 刷新窗口失败: {e}")
    
    def on_window_selected(self, event=None):
        """窗口选择改变时"""
        selected = self.window_var.get()
        if selected:
            title = selected.split(" (")[0]  # 提取窗口标题
            self.log(f"👆 已选择: {title}")
    
    def select_target_window(self):
        """选择目标窗口"""
        selected = self.window_var.get()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        try:
            # 提取窗口标题
            self.target_title = selected.split(" (")[0]
            
            # 查找窗口对象
            windows = gw.getWindowsWithTitle(self.target_title)
            if windows:
                self.target_window = windows[0]
                self.selected_label.config(text=f"✅ 已选择: {self.target_title}", foreground="green")
                self.log(f"✅ 目标窗口已设置: {self.target_title}")
                
                # 激活窗口进行测试
                self.target_window.activate()
                time.sleep(0.5)
                
                return True
            else:
                self.log(f"❌ 无法找到窗口: {self.target_title}")
                return False
                
        except Exception as e:
            self.log(f"❌ 选择窗口失败: {e}")
            return False
    
    def start_monitoring(self):
        """开始监控"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择目标窗口")
            return
        
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        self.log(f"🚀 开始监控窗口: {self.target_title}")
        self.status_var.set("监控中...")
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        self.log("⏹️ 监控已停止")
        self.status_var.set("已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 检查窗口是否还存在
                if not self._check_window_exists():
                    self.log("❌ 目标窗口已关闭，停止监控")
                    self.root.after(0, self.stop_monitoring)
                    break
                
                # 截取当前窗口
                current_screenshot = self._capture_target_window()
                if current_screenshot is None:
                    time.sleep(2)
                    continue
                
                # 检测是否有新消息
                if self._detect_new_message(current_screenshot):
                    self.log("📨 检测到新消息，正在处理...")
                    
                    # 识别消息内容（这里简化处理，实际可以加入OCR）
                    # 为了演示，我们假设收到了一条消息
                    message_content = "收到新消息"  # 实际应该用OCR识别
                    
                    # 获取AI回复
                    ai_reply = self._get_ai_reply(message_content)
                    if ai_reply:
                        # 发送回复
                        if self._send_reply_to_wechat(ai_reply):
                            self.log(f"✅ 已自动回复: {ai_reply[:30]}...")
                        else:
                            self.log("❌ 发送回复失败")
                    else:
                        self.log("❌ 获取AI回复失败")
                
                # 更新上次截图
                self.last_screenshot = current_screenshot
                
                # 等待下次检查
                time.sleep(3)
                
            except Exception as e:
                self.log(f"❌ 监控出错: {e}")
                time.sleep(5)
    
    def _check_window_exists(self):
        """检查目标窗口是否还存在"""
        try:
            windows = gw.getWindowsWithTitle(self.target_title)
            return len(windows) > 0 and windows[0].visible
        except:
            return False
    
    def _capture_target_window(self):
        """截取目标窗口"""
        try:
            if not self.target_window:
                return None
            
            # 获取窗口位置
            left = self.target_window.left
            top = self.target_window.top
            width = self.target_window.width
            height = self.target_window.height
            
            # 截图
            screenshot = ImageGrab.grab(bbox=(left, top, left + width, top + height))
            return screenshot
            
        except Exception as e:
            self.log(f"❌ 截图失败: {e}")
            return None
    
    def _detect_new_message(self, current_screenshot):
        """检测是否有新消息"""
        if self.last_screenshot is None:
            return False
        
        try:
            # 简单的图像对比检测
            current_array = np.array(current_screenshot)
            last_array = np.array(self.last_screenshot)
            
            # 计算差异
            diff = cv2.absdiff(current_array, last_array)
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_RGB2GRAY)
            
            # 计算变化程度
            change_pixels = np.sum(gray_diff > 30)
            total_pixels = gray_diff.shape[0] * gray_diff.shape[1]
            change_ratio = change_pixels / total_pixels
            
            # 如果变化超过阈值，认为有新消息
            return change_ratio > 0.01  # 1%的像素发生变化
            
        except Exception as e:
            self.log(f"❌ 消息检测失败: {e}")
            return False
    
    def _get_ai_reply(self, message):
        """获取AI回复"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "anthropic/claude-3.5-sonnet",
                "messages": [
                    {"role": "system", "content": "你是一个友善的AI助手，请用中文简洁回复，不超过50字。"},
                    {"role": "user", "content": message}
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = requests.post(f"{self.base_url}/chat/completions", 
                                   headers=headers, json=data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"].strip()
            else:
                self.log(f"❌ API调用失败: {response.status_code}")
                return None
                
        except Exception as e:
            self.log(f"❌ 获取AI回复失败: {e}")
            return None
    
    def _send_reply_to_wechat(self, reply):
        """发送回复到微信"""
        try:
            # 激活目标窗口
            self.target_window.activate()
            time.sleep(0.5)
            
            # 计算输入框位置（估算）
            window_left = self.target_window.left
            window_top = self.target_window.top
            window_width = self.target_window.width
            window_height = self.target_window.height
            
            # 输入框通常在窗口底部中间
            input_x = window_left + int(window_width * 0.5)
            input_y = window_top + int(window_height * 0.85)
            
            # 点击输入框
            pyautogui.click(input_x, input_y)
            time.sleep(0.3)
            
            # 清空输入框
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            
            # 输入回复内容
            pyautogui.write(reply, interval=0.02)
            time.sleep(0.5)
            
            # 发送消息
            pyautogui.press('enter')
            
            return True
            
        except Exception as e:
            self.log(f"❌ 发送消息失败: {e}")
            return False
    
    def test_screenshot(self):
        """测试截图功能"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择目标窗口")
            return
        
        try:
            screenshot = self._capture_target_window()
            if screenshot:
                screenshot.save("test_screenshot.png")
                self.log("✅ 测试截图已保存: test_screenshot.png")
                messagebox.showinfo("成功", "截图已保存为 test_screenshot.png")
            else:
                self.log("❌ 截图失败")
                
        except Exception as e:
            self.log(f"❌ 测试截图失败: {e}")
    
    def test_ai_reply(self):
        """测试AI回复"""
        message = self.test_message_var.get().strip()
        if not message:
            messagebox.showwarning("警告", "请输入测试消息")
            return
        
        self.log(f"🤖 正在获取AI回复: {message}")
        self.reply_text.delete(1.0, tk.END)
        self.reply_text.insert(tk.END, "正在获取AI回复...")
        
        def get_reply():
            reply = self._get_ai_reply(message)
            if reply:
                self.reply_text.delete(1.0, tk.END)
                self.reply_text.insert(tk.END, reply)
                self.log(f"✅ AI回复: {reply}")
            else:
                self.reply_text.delete(1.0, tk.END)
                self.reply_text.insert(tk.END, "获取回复失败")
                self.log("❌ 获取AI回复失败")
        
        threading.Thread(target=get_reply, daemon=True).start()
    
    def send_test_reply(self):
        """发送测试回复"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择目标窗口")
            return
        
        reply = self.reply_text.get(1.0, tk.END).strip()
        if not reply:
            messagebox.showwarning("警告", "没有回复内容")
            return
        
        self.log(f"📤 正在发送测试回复: {reply[:30]}...")
        
        if self._send_reply_to_wechat(reply):
            self.log("✅ 测试回复发送成功")
            messagebox.showinfo("成功", "测试回复已发送")
        else:
            self.log("❌ 测试回复发送失败")
            messagebox.showerror("失败", "发送失败，请检查窗口状态")
    
    def test_send_message(self):
        """测试发送消息功能"""
        if not self.target_window:
            messagebox.showwarning("警告", "请先选择目标窗口")
            return
        
        test_msg = "这是一条测试消息"
        self.log(f"🧪 发送测试消息: {test_msg}")
        
        if self._send_reply_to_wechat(test_msg):
            self.log("✅ 测试消息发送成功")
        else:
            self.log("❌ 测试消息发送失败")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    app = PreciseWeChatReplier()
    app.run()

if __name__ == "__main__":
    main()
