"""
AI回复生成模块
"""
import openai
import requests
import json
import logging
from typing import Optional, Dict, Any
from config import config

logger = logging.getLogger(__name__)

class AIResponder:
    def __init__(self):
        self.provider = config.AI_PROVIDER
        self.conversation_history = {}  # 存储对话历史
        self.max_history_length = 10   # 最大历史记录长度
        
        self._init_ai_client()
    
    def _init_ai_client(self):
        """初始化AI客户端"""
        if self.provider == "openai":
            self._init_openai()
        elif self.provider == "local":
            self._init_local_ai()
        else:
            logger.error(f"不支持的AI提供商: {self.provider}")
    
    def _init_openai(self):
        """初始化OpenAI客户端"""
        try:
            if not config.OPENAI_API_KEY:
                logger.error("OpenAI API Key未设置")
                return
            
            openai.api_key = config.OPENAI_API_KEY
            if config.OPENAI_BASE_URL:
                openai.base_url = config.OPENAI_BASE_URL
            
            logger.info("OpenAI客户端初始化成功")
            
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {e}")
    
    def _init_local_ai(self):
        """初始化本地AI"""
        try:
            # 测试本地AI连接
            response = requests.get(config.LOCAL_AI_URL.replace('/api/generate', '/api/tags'), 
                                  timeout=5)
            if response.status_code == 200:
                logger.info("本地AI连接成功")
            else:
                logger.warning("本地AI连接异常")
                
        except Exception as e:
            logger.error(f"本地AI初始化失败: {e}")
    
    def generate_reply(self, message: str, contact_name: str = "unknown") -> Optional[str]:
        """生成AI回复"""
        try:
            if self.provider == "openai":
                return self._generate_openai_reply(message, contact_name)
            elif self.provider == "local":
                return self._generate_local_reply(message, contact_name)
            else:
                logger.error(f"不支持的AI提供商: {self.provider}")
                return None
                
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return None
    
    def _generate_openai_reply(self, message: str, contact_name: str) -> Optional[str]:
        """使用OpenAI生成回复"""
        try:
            # 构建对话历史
            messages = self._build_conversation_history(message, contact_name)
            
            # 调用OpenAI API
            client = openai.OpenAI(
                api_key=config.OPENAI_API_KEY,
                base_url=config.OPENAI_BASE_URL
            )
            
            response = client.chat.completions.create(
                model=config.OPENAI_MODEL,
                messages=messages,
                max_tokens=config.MAX_REPLY_LENGTH,
                temperature=0.7,
                timeout=10
            )
            
            reply = response.choices[0].message.content.strip()
            
            # 更新对话历史
            self._update_conversation_history(contact_name, message, reply)
            
            logger.info(f"OpenAI回复生成成功: {reply[:50]}...")
            return reply
            
        except Exception as e:
            logger.error(f"OpenAI回复生成失败: {e}")
            return None
    
    def _generate_local_reply(self, message: str, contact_name: str) -> Optional[str]:
        """使用本地AI生成回复"""
        try:
            # 构建提示词
            prompt = self._build_local_prompt(message, contact_name)
            
            # 调用本地AI API
            payload = {
                "model": config.LOCAL_AI_MODEL,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "max_tokens": config.MAX_REPLY_LENGTH
                }
            }
            
            response = requests.post(
                config.LOCAL_AI_URL,
                json=payload,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                reply = result.get("response", "").strip()
                
                # 更新对话历史
                self._update_conversation_history(contact_name, message, reply)
                
                logger.info(f"本地AI回复生成成功: {reply[:50]}...")
                return reply
            else:
                logger.error(f"本地AI API调用失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"本地AI回复生成失败: {e}")
            return None
    
    def _build_conversation_history(self, current_message: str, contact_name: str) -> list:
        """构建OpenAI对话历史"""
        messages = [
            {"role": "system", "content": config.SYSTEM_PROMPT}
        ]
        
        # 添加历史对话
        if contact_name in self.conversation_history:
            history = self.conversation_history[contact_name]
            for item in history[-self.max_history_length:]:  # 只保留最近的对话
                messages.append({"role": "user", "content": item["user"]})
                messages.append({"role": "assistant", "content": item["assistant"]})
        
        # 添加当前消息
        messages.append({"role": "user", "content": current_message})
        
        return messages
    
    def _build_local_prompt(self, message: str, contact_name: str) -> str:
        """构建本地AI提示词"""
        prompt = config.SYSTEM_PROMPT + "\n\n"
        
        # 添加历史对话
        if contact_name in self.conversation_history:
            history = self.conversation_history[contact_name]
            for item in history[-3:]:  # 只保留最近3轮对话
                prompt += f"用户: {item['user']}\n助手: {item['assistant']}\n\n"
        
        # 添加当前消息
        prompt += f"用户: {message}\n助手: "
        
        return prompt
    
    def _update_conversation_history(self, contact_name: str, user_message: str, ai_reply: str):
        """更新对话历史"""
        if contact_name not in self.conversation_history:
            self.conversation_history[contact_name] = []
        
        self.conversation_history[contact_name].append({
            "user": user_message,
            "assistant": ai_reply,
            "timestamp": time.time()
        })
        
        # 限制历史记录长度
        if len(self.conversation_history[contact_name]) > self.max_history_length:
            self.conversation_history[contact_name] = \
                self.conversation_history[contact_name][-self.max_history_length:]
    
    def should_reply(self, message: str, contact_name: str = "") -> bool:
        """判断是否应该回复该消息"""
        if not message:
            return False
        
        # 检查忽略关键词
        for keyword in config.IGNORE_KEYWORDS:
            if keyword in message:
                logger.info(f"消息包含忽略关键词 '{keyword}'，跳过回复")
                return False
        
        # 检查忽略的联系人
        if contact_name in config.IGNORE_CONTACTS:
            logger.info(f"联系人 '{contact_name}' 在忽略列表中，跳过回复")
            return False
        
        # 检查消息长度
        if len(message) < 2:
            logger.info("消息太短，跳过回复")
            return False
        
        # 检查是否为重复消息
        if self._is_duplicate_message(message, contact_name):
            logger.info("检测到重复消息，跳过回复")
            return False
        
        return True
    
    def _is_duplicate_message(self, message: str, contact_name: str) -> bool:
        """检查是否为重复消息"""
        if contact_name not in self.conversation_history:
            return False
        
        history = self.conversation_history[contact_name]
        if not history:
            return False
        
        # 检查最近的消息是否重复
        recent_messages = [item["user"] for item in history[-3:]]
        return message in recent_messages
    
    def get_fallback_reply(self, message: str) -> str:
        """获取备用回复（当AI生成失败时使用）"""
        fallback_replies = [
            "抱歉，我现在有点忙，稍后回复您。",
            "收到您的消息了，让我想想怎么回复。",
            "好的，我知道了。",
            "谢谢您的消息！",
            "嗯嗯，明白了。"
        ]
        
        import random
        return random.choice(fallback_replies)

import time
