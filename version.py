"""
版本信息
"""

__version__ = "1.0.0"
__author__ = "WeChatAI Team"
__email__ = "<EMAIL>"
__description__ = "微信自动回复智能体 - 基于UI自动化和OCR技术的智能回复系统"

# 版本历史
VERSION_HISTORY = {
    "1.0.0": {
        "date": "2024-01-01",
        "features": [
            "基础的微信窗口监控功能",
            "OCR文字识别（PaddleOCR + Tesseract）",
            "OpenAI GPT智能回复",
            "本地AI模型支持",
            "图形用户界面",
            "消息过滤和频率限制",
            "自动化消息发送",
            "日志记录和错误处理"
        ],
        "improvements": [
            "初始版本发布"
        ],
        "bug_fixes": []
    }
}

def get_version_info():
    """获取版本信息"""
    return {
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "description": __description__
    }

def print_version():
    """打印版本信息"""
    print(f"微信自动回复智能体 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")

if __name__ == "__main__":
    print_version()
