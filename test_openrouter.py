"""
测试OpenRouter API连接
"""
import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_openrouter_connection():
    """测试OpenRouter API连接"""
    print("🧪 测试OpenRouter API连接")
    print("=" * 50)
    
    # 获取配置
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
    
    if not api_key:
        print("❌ 错误：未找到API Key")
        print("请检查.env文件中的OPENAI_API_KEY配置")
        return False
    
    print(f"📋 配置信息:")
    print(f"   API Key: {api_key[:20]}...{api_key[-10:]}")
    print(f"   Base URL: {base_url}")
    
    # 测试连接
    print(f"\n🔍 测试API连接...")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        # 获取模型列表
        print("正在获取可用模型列表...")
        response = requests.get(f"{base_url}/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API连接成功！")
            
            models_data = response.json()
            models = models_data.get("data", [])
            print(f"📊 找到 {len(models)} 个可用模型")
            
            # 显示前10个模型
            print(f"\n🤖 热门模型列表:")
            popular_models = [
                "anthropic/claude-3.5-sonnet",
                "openai/gpt-4-turbo",
                "openai/gpt-3.5-turbo",
                "meta-llama/llama-3-8b-instruct",
                "google/gemini-pro"
            ]
            
            available_popular = []
            for model in models:
                model_id = model.get("id", "")
                if model_id in popular_models:
                    available_popular.append(model_id)
                    print(f"   ✅ {model_id}")
            
            if not available_popular:
                print("   ⚠️ 未找到热门模型，显示前5个可用模型:")
                for i, model in enumerate(models[:5]):
                    print(f"   • {model.get('id', 'Unknown')}")
            
            return True
            
        else:
            print(f"❌ API连接失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 连接超时，请检查网络连接")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_chat_completion():
    """测试聊天完成功能"""
    print(f"\n🤖 测试AI聊天功能")
    print("=" * 50)
    
    # 获取配置
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1")
    model = "anthropic/claude-3.5-sonnet"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试消息
    test_message = "你好，请用中文简单介绍一下你自己，不超过50字。"
    
    print(f"📝 测试消息: {test_message}")
    print(f"🎯 使用模型: {model}")
    
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": test_message}
        ],
        "max_tokens": 150,
        "temperature": 0.7
    }
    
    try:
        print(f"\n⏳ 正在发送请求...")
        response = requests.post(f"{base_url}/chat/completions", 
                               headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            reply = result["choices"][0]["message"]["content"]
            
            print(f"✅ AI回复成功！")
            print(f"🤖 AI回复: {reply}")
            
            # 显示使用统计
            usage = result.get("usage", {})
            if usage:
                print(f"\n📊 使用统计:")
                print(f"   输入tokens: {usage.get('prompt_tokens', 'N/A')}")
                print(f"   输出tokens: {usage.get('completion_tokens', 'N/A')}")
                print(f"   总计tokens: {usage.get('total_tokens', 'N/A')}")
            
            return True
            
        else:
            print(f"❌ 聊天请求失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 OpenRouter API 测试工具")
    print("=" * 60)
    
    # 检查环境变量文件
    if not os.path.exists(".env"):
        print("❌ 错误：未找到.env配置文件")
        print("请确保.env文件存在并包含正确的API配置")
        return
    
    success_count = 0
    total_tests = 2
    
    # 测试连接
    if test_openrouter_connection():
        success_count += 1
    
    # 测试聊天
    if test_chat_completion():
        success_count += 1
    
    # 显示结果
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果汇总")
    print("=" * 60)
    print(f"成功测试: {success_count}/{total_tests}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！OpenRouter API配置正确。")
        print(f"✅ 您现在可以启动微信自动回复程序了。")
    elif success_count > 0:
        print(f"\n⚠️ 部分测试通过，请检查失败的测试项。")
    else:
        print(f"\n❌ 所有测试失败，请检查API配置。")
    
    print(f"\n💡 使用提示:")
    print(f"1. 运行 python main.py 启动主程序")
    print(f"2. 运行 python config_manager.py 管理配置")
    print(f"3. 查看 openrouter_info.html 了解更多信息")

if __name__ == "__main__":
    main()
