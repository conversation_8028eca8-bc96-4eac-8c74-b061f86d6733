@echo off
chcp 65001 >nul
echo 微信自动回复智能体启动脚本
echo ================================

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import pyautogui, cv2, PIL" >nul 2>&1
if errorlevel 1 (
    echo 警告: 部分依赖包未安装，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 检查配置文件...
if not exist .env (
    echo 提示: 未找到.env配置文件，请复制.env.example并配置API密钥
    copy .env.example .env >nul
    echo 已创建.env文件，请编辑后重新运行
    pause
    exit /b 0
)

echo 启动程序...
python main.py

if errorlevel 1 (
    echo 程序异常退出
    pause
)
