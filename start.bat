@echo off
chcp 65001 >nul
echo 🤖 微信自动回复智能体启动脚本
echo ================================

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import pyautogui, cv2, PIL" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 警告: 部分依赖包未安装，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 检查配置文件...
if not exist .env (
    echo ⚠️ 提示: 未找到.env配置文件，正在创建...
    copy .env.example .env >nul
    echo ✅ 已创建.env文件（OpenRouter AI已预配置）
)

echo 测试API连接...
python test_openrouter.py >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 警告: API连接测试失败，但程序仍可启动
) else (
    echo ✅ API连接正常
)

echo.
echo 🚀 启动程序...
echo ================================
python main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    echo 💡 提示: 可以运行以下命令进行诊断:
    echo    python test_modules.py    - 测试各模块
    echo    python demo.py           - 功能演示
    echo    python config_manager.py - 配置管理
    pause
)
