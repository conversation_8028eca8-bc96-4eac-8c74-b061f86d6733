# 🎯 精准微信自动回复器使用指南

## 🚀 全新解决方案

我为您创建了一个**精准微信自动回复器**，完全按照您的要求实现：

✅ **1. 识别特定微信窗口标题** - 精确选择要监控的单个微信对话窗口
✅ **2. 获取AI回复** - 调用OpenRouter API获取智能回复
✅ **3. 填入回复区** - 自动将回复内容输入到微信输入框
✅ **4. 点击发送** - 自动发送回复消息

## 📋 使用步骤

### 第一步：启动程序

**方法一：双击启动**
```bash
精准回复器.bat
```

**方法二：命令行启动**
```bash
python 精准微信回复器.py
```

### 第二步：选择目标窗口

1. **打开要监控的微信对话**
   - 打开微信PC版
   - 进入您要监控的特定聊天窗口
   - 确保窗口标题显示为"微信 - 联系人名字"

2. **刷新窗口列表**
   - 在程序中点击"🔄 刷新窗口"
   - 查看下拉列表中的微信窗口

3. **选择目标窗口**
   - 从下拉列表选择要监控的窗口
   - 点击"✅ 选择此窗口"
   - 确认显示"✅ 已选择: 窗口标题"

### 第三步：测试功能

**测试截图功能**
- 点击"📸 测试截图"
- 确认能正确截取目标窗口

**测试AI回复**
- 在"测试消息"框输入消息
- 点击"🤖 获取AI回复"
- 查看AI回复内容

**测试发送功能**
- 点击"📤 发送回复"
- 确认消息能正确发送到微信

### 第四步：开始自动监控

1. **启动监控**
   - 点击"🚀 开始监控"
   - 观察监控日志

2. **测试自动回复**
   - 让朋友发送测试消息
   - 观察程序是否自动回复

3. **停止监控**
   - 点击"⏹️ 停止监控"

## 🎛️ 界面功能详解

### 第一步区域：选择目标窗口
- **🔄 刷新窗口**：重新检测所有微信窗口
- **窗口下拉列表**：显示所有检测到的微信窗口
- **✅ 选择此窗口**：确认选择当前窗口作为监控目标
- **状态显示**：显示当前选择的窗口标题

### 第二步区域：监控控制
- **🚀 开始监控**：启动自动监控和回复
- **⏹️ 停止监控**：停止监控
- **📸 测试截图**：测试窗口截图功能
- **🧪 测试发送**：发送测试消息
- **监控日志**：显示实时监控状态和操作日志

### 第三步区域：手动测试
- **测试消息输入**：输入要测试的消息内容
- **🤖 获取AI回复**：测试AI回复生成功能
- **AI回复显示**：显示生成的回复内容
- **📤 发送回复**：手动发送回复到微信

## 🔧 工作原理

### 1. 窗口识别
- 扫描所有包含"微信"的窗口
- 精确匹配指定的窗口标题
- 只监控选定的单个窗口

### 2. 消息检测
- 定期截取目标窗口
- 通过图像对比检测界面变化
- 识别新消息的出现

### 3. AI回复生成
- 调用OpenRouter API
- 使用Claude 3.5 Sonnet模型
- 生成简洁的中文回复

### 4. 自动发送
- 激活目标微信窗口
- 定位输入框位置
- 输入回复内容并发送

## 📝 使用示例

### 示例1：监控个人聊天
1. 打开与"张三"的聊天窗口
2. 选择"微信 - 张三"窗口
3. 开始监控
4. 当张三发消息时，自动回复

### 示例2：监控群聊
1. 打开"工作群"聊天窗口
2. 选择"微信 - 工作群"窗口
3. 开始监控
4. 当群里有消息时，自动回复

### 示例3：测试功能
1. 打开"文件传输助手"
2. 选择"微信 - 文件传输助手"窗口
3. 使用手动测试功能验证
4. 确认功能正常后开始监控

## ⚙️ 配置说明

### API配置
程序自动读取`.env`文件中的配置：
```
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://openrouter.ai/api/v1
```

### 监控参数
- **截图间隔**：3秒
- **变化阈值**：1%像素变化
- **回复模型**：Claude 3.5 Sonnet
- **回复长度**：最多50字

## 🛡️ 安全特性

### 精确控制
- 只监控指定的单个窗口
- 不会影响其他微信对话
- 可以随时停止监控

### 智能检测
- 基于图像变化检测新消息
- 避免重复回复
- 自动处理窗口状态

### 人工监督
- 实时监控日志
- 手动测试功能
- 随时可以干预

## 🔍 故障排除

### 问题1：找不到微信窗口
**解决方案：**
- 确保微信PC版已打开
- 确保目标聊天窗口已打开
- 点击"🔄 刷新窗口"重新检测

### 问题2：截图失败
**解决方案：**
- 确保微信窗口没有被最小化
- 确保窗口没有被其他程序遮挡
- 尝试重新选择窗口

### 问题3：发送消息失败
**解决方案：**
- 确保微信窗口处于前台
- 检查输入框是否可见
- 尝试手动点击输入框后再测试

### 问题4：AI回复失败
**解决方案：**
- 检查网络连接
- 确认API密钥正确
- 查看监控日志中的错误信息

## 💡 使用技巧

### 1. 窗口选择
- 选择具体的聊天窗口，不要选择微信主界面
- 确保窗口标题包含联系人或群名

### 2. 测试建议
- 首次使用建议在文件传输助手中测试
- 确认所有功能正常后再用于重要对话

### 3. 监控优化
- 保持目标窗口可见
- 避免频繁切换聊天对象
- 定期检查监控状态

### 4. 回复质量
- 可以通过手动测试调整回复效果
- 观察AI回复的质量和适当性

## 🎉 开始使用

现在您可以：

1. **启动程序**：双击`精准回复器.bat`
2. **选择窗口**：选择要监控的特定微信对话
3. **测试功能**：使用手动测试验证各项功能
4. **开始监控**：启动自动监控和回复
5. **享受智能对话**：体验精准的自动回复服务

**精准微信自动回复器已经为您准备就绪！** 🎊
